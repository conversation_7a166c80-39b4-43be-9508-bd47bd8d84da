<script setup>
import { ref, onMounted, onUnmounted, reactive } from "vue";
import vueMxObject from "@/utils/VueMxGraphLoader";
import BusinessInformation from "./businessInformation.vue";
import { businessTopology } from "@/api/modules/business_topo";
import BusinessForm from "./businessForm.vue";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
const { vueMxClient, vueMxUtils, vueMxGraph } = vueMxObject;
 // 获取路由实例
 const route = useRoute();
onMounted(() => {
  drawTopology();
});
onUnmounted(() => {
  graph.destroy();
  graph = null;
});
const props = defineProps({
  businessId: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
});
const tooltipStyle = ref({});
const tooltip = reactive({
  showTooltip: false,
  tooltipName: "",
  tooltipIp: "",
  tooltipProblem: [],
  extraInfo: [{ key: "", value: "", html: false }],
});
const data = reactive({
  businessId: "",
  importDialog: false,
  exportDialog: false,
  informationDialog: false,
  informationVisible: false,
  xml: "",
  exportXml: "",
  selectBusiness: null,
  width: 0,
  toolbarMinimized: false,
  addBusinessFormVisible: false,
  editBusinessFormVisible: false,
  deleteConfirmVisible: false,
  currentBusinessToEdit: null,
  currentBusinessToDelete: null,
  isNodeSelected: false,
});
let graph;

// 根据status获取状态颜色
function getStatusColor(status) {
  const statusColorMap = {
    'success': '#52c41a',  // 绿色 - 正常状态
    'danger': '#f5222d',   // 红色 - 危险状态
    'warning': '#faad14',  // 黄色 - 警告状态
  };
  return statusColorMap[status] || '#8c8c8c'; // 默认灰色
}

// 根据status获取背景颜色
function getStatusBgColor(status) {
  const bgColorMap = {
    'success': '#f6ffed',
    'danger': '#fff2e8',
    'warning': '#fff7e6',
  };
  return bgColorMap[status] || '#f5f5f5'; // 默认浅灰色
}

// 根据类型获取中文名称
function getTypeLabel(type) {
  const typeMap = {
    'service': '业务',
    'oracle': 'oracle数据库',
    'pg': 'pg数据库',
    'nginx': 'nginx中间件',
    'iis': 'iis中间件',
    'origin': 'origin',
    'interface': '接口',
    'firewall': '防火墙',
    'switch': '交换机',
  };
  return typeMap[type] || type;
}

// 根据status获取中文状态描述和样式类名
function getStatusLabel(status) {
  const statusMap = {
    'success': { label: '正常运行', class: 'status-success' },
    'danger': { label: '故障', class: 'status-danger' },
    'warning': { label: '警告', class: 'status-warning' },
  };
  return statusMap[status] || { label: '未知', class: 'status-unknown' };
}

// 添加文本截断函数
function truncateText(text, maxLength) {
  if (!text) return '';
  if (text.length > maxLength) {
    return text.substring(0, maxLength) + '...';
  }
  return text;
}

// 处理问题数据，确保是一维数组
function processProblemData(problemData) {
  if (!problemData) return [];
  
  // 如果是嵌套数组，进行扁平化处理
  if (Array.isArray(problemData) && problemData.length > 0 && Array.isArray(problemData[0])) {
    return problemData[0];
  }
  
  return problemData;
}

// 根据优先级获取样式类名
function getPriorityClass(priority) {
  if (!priority) return 'priority-default';
  
  if (priority === '灾难') return 'priority-disaster';
  if (priority === '严重' || priority.includes('严重')) return 'priority-danger';
  if (priority === '警告' || priority.includes('警告')) return 'priority-warning';
  if (priority === '信息' || priority.includes('信息')) return 'priority-info';
  
  return 'priority-default';
}

// 格式化时间显示
function formatDateTime(dateTimeStr) {
  if (!dateTimeStr) return '';
  
  try {
    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) {
      // 如果无法解析为Date对象，直接返回原始字符串
      return dateTimeStr;
    }
    
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } catch (e) {
    // 如果解析出错，返回原始字符串
    return dateTimeStr;
  }
}

// 获取告警级别对应的类型和颜色
function getAlertType(priority) {
  switch (priority) {
    case '灾难':
      return { type: 'danger', color: '#E45959' };
    case '严重':
      return { type: 'danger', color: '#E97659' };
    case '一般严重':
      return { type: 'warning', color: '#FFA059' };
    case '警告':
      return { type: 'warning', color: '#FFC859' };
    case '信息':
      return { type: 'info', color: '#7499FF' };
    default:
      return { type: 'info', color: '#909399' };
  }
}

function drawTopology() {
  data.businessId = props.businessId;
  
  businessTopology(data.businessId).then((res) => {
    
    // 根据新的API结构获取节点和连接数据
    const nodeData = res.data && res.data.node ? res.data.node : [];
    const linkData = res.data && res.data.link ? res.data.link : [];
    
    if (nodeData.length === 0) {
      return; // 如果没有节点数据，直接返回
    }
    
    // 打印API返回的节点数据
    if (!vueMxClient.isBrowserSupported()) {
      vueMxUtils.error("浏览器不支持", 200, false);
      return;
    }
    const container = document.getElementById("drawioContainer");
    if (!container) {

      return;
    }
    
    graph = new vueMxGraph(container);
    const model = graph.getModel();
    // 基本设置
    graph.isCellFoldable = function () { return false; };
    graph.setTooltips(false); 
    graph.setPanning(true);
    graph.setAutoSizeCells(false);
    graph.autoscroll = false;
    
    // 禁用右键菜单
    mxEvent.disableContextMenu(container);
    
    graph.container.addEventListener("contextmenu", function (evt) {
      evt.preventDefault();
    });

    mxEvent.addMouseWheelListener(function (evt, scrollUp) {
      if (evt.ctrlKey && scrollUp) {
        graph.zoomIn();
      }
      if (evt.ctrlKey && !scrollUp) {
        graph.zoomOut();
      }
      if (evt.preventDefault) {
        evt.preventDefault();
      } else {
        evt.returnValue = false;
      }
    }, container);
    
    // 自定义鼠标指针
    graph.getCursorForCell = function (cell) {
      if (cell != null && cell.value != null && cell.vertex == 1) {
        return "pointer";
      }
    };

    // 添加点击事件
    graph.addListener(mxEvent.CLICK, (sender, evt) => {
      const cell = evt.getProperty("cell");
      
      // 清除之前的选择状态
      graph.clearSelection();
      
      if (cell && cell.userData) {
        // 选中当前节点
        graph.setSelectionCell(cell);
        
        // 无论是集群节点还是子节点，都按照集群处理
        let userData = cell.userData;
        
        // 如果是子节点，但需要携带完整的信息
        if (!userData.isGroup) {
          // 先获取父节点的数据，如果可以获取到
          let parentInfo = {};
          if (cell.parent && cell.parent.userData) {
            // 从父节点获取集群信息
            parentInfo = { ...cell.parent.userData };
            // 移除父节点的子节点数组，以避免循环引用
            if (parentInfo.cluster) {
              delete parentInfo.cluster;
            }
          }
          
          // 创建一个新的对象，模拟集群格式
          userData = {
            ...userData,
            ...parentInfo,  // 合并父节点的信息
            isGroup: true,  // 标记为集群类型
            // 创建cluster数组，只包含当前节点
            cluster: [{ ...userData }]
          };
        }
        
        data.selectBusiness = userData;
        data.isNodeSelected = true;  // 设置选中状态为true
      } else {
        // 如果点击的不是节点，清除选择
        data.selectBusiness = null;
        data.isNodeSelected = false;
      }
    });

    // 添加双击事件
    graph.addListener(mxEvent.DOUBLE_CLICK, (sender, evt) => {
      const cell = evt.getProperty("cell");
      if (cell && cell.userData) {
        // 无论是集群节点还是子节点，都按照集群处理
        let userData = cell.userData;
        
        // 如果是子节点，但需要携带完整的信息
        if (!userData.isGroup) {
          // 先获取父节点的数据，如果可以获取到
          let parentInfo = {};
          if (cell.parent && cell.parent.userData) {
            // 从父节点获取集群信息
            parentInfo = { ...cell.parent.userData };
            // 移除父节点的子节点数组，以避免循环引用
            if (parentInfo.cluster) {
              delete parentInfo.cluster;
            }
          }
          
          // 创建一个新的对象，模拟集群格式
          userData = {
            ...userData,
            ...parentInfo,  // 合并父节点的信息
            isGroup: true,  // 标记为集群类型
            // 创建cluster数组，只包含当前节点
            cluster: [{ ...userData }]
          };
        }
        
        data.selectBusiness = userData;
        data.isNodeSelected = true;  // 设置选中状态为true
        opneInformation();  // 双击时打开信息面板
        
        // 阻止事件冒泡，避免触发其他事件
        evt.consume();
      }
    });

    // 为tooltip添加事件
    const track = new mxCellTracker(graph);
    track.mouseMove = (sender, me) => {
      const cell = track.getCell(me);
      if (cell?.userData) {
        const nodeData = cell.userData;
        
        // 区分集群和子节点处理
        if (nodeData.isGroup) {
          // 集群节点显示更多信息
          tooltip.showTooltip = true;
          tooltip.tooltipName = nodeData.groupName || nodeData.productName || nodeData.serviceName || "";
          tooltip.tooltipIp = nodeData.ip || "";
          tooltip.tooltipProblem = processProblemData(nodeData.problem);
          
          const statusInfo = getStatusLabel(nodeData.status);
          
          // 添加集群特有信息
          const tooltipData = {
            类型: getTypeLabel(nodeData.type) || "集群",
            状态: `<span class="status-label ${statusInfo.class}">${statusInfo.label}</span>`,
            版本: nodeData.currentVersion || "未知",
            安装路径: nodeData.installDirForDisplay || "",
            服务名称: nodeData.serviceName || "",
            实例名称: nodeData.instanceName || ""
          };
          
          // 按序转换为数组，过滤空值
          tooltip.extraInfo = Object.entries(tooltipData)
            .filter(([_, value]) => value && value !== "未知" && value !== "")
            .map(([key, value]) => ({ key, value, html: key === '状态' }));
        } 
        else if (nodeData.ip) {
          // 子节点显示
          tooltip.showTooltip = true;
          
          // 如果有显示名称则优先显示，否则显示IP
          tooltip.tooltipName = nodeData.name || nodeData.productName || nodeData.serviceName || nodeData.ip || "";
          tooltip.tooltipIp = nodeData.ip || "";
          tooltip.tooltipProblem = processProblemData(nodeData.problem);
          
          const statusInfo = getStatusLabel(nodeData.status);
          
          // 子节点特有信息
          const tooltipData = {
            类型: getTypeLabel(nodeData.type) || "业务",
            状态: `<span class="status-label ${statusInfo.class}">${statusInfo.label}</span>`,
            产品名称: nodeData.productName || ""
          };
          
          // 按序转换为数组，过滤空值
          tooltip.extraInfo = Object.entries(tooltipData)
            .filter(([_, value]) => value && value !== "")
            .map(([key, value]) => ({ key, value, html: key === '状态' }));
        } 
        else {
          tooltip.showTooltip = false;
          return;
        }
        
        // 获取鼠标在页面上的位置
        const mouseX = me.getEvent().clientX;
        const mouseY = me.getEvent().clientY;
        
        // 设置tooltip位置，考虑窗口边界
        const tooltipWidth = 350;
        const tooltipHeight = 200;
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        let finalX = mouseX + 15;
        let finalY = mouseY + 15;
        
        if (finalX + tooltipWidth > windowWidth) {
          finalX = mouseX - tooltipWidth - 15;
        }
        
        if (finalY + tooltipHeight > windowHeight) {
          finalY = mouseY - tooltipHeight - 15;
        }
        
        tooltipStyle.value = {
          left: `${finalX}px`,
          top: `${finalY}px`,
          position: 'fixed',
          zIndex: 9999
        };
      } else {
        tooltip.showTooltip = false;
      }
    };
    track.mouseLeave = () => { 
      tooltip.showTooltip = false; 
    };

    // 添加全局鼠标移动监听器来处理拖拽
    graph.addMouseListener({
      mouseDown: function(sender, me) {
        if (tooltip.showTooltip) {
          tooltip.showTooltip = false;
        }
      },
      mouseMove: function(sender, me) {},
      mouseUp: function(sender, me) {}
    });

    model.beginUpdate();
    try {
      if (data.width === 0) {
        data.width = container.offsetWidth;
      }
      // 定义边缘样式
      let edgeStyle = graph.getStylesheet().getDefaultEdgeStyle();
      edgeStyle[mxConstants.STYLE_ROUNDED] = true;
      edgeStyle[mxConstants.STYLE_STROKEWIDTH] = 2;
      edgeStyle[mxConstants.STYLE_STROKECOLOR] = '#1890ff';
      edgeStyle[mxConstants.STYLE_ENDARROW] = mxConstants.ARROW_CLASSIC;
      edgeStyle[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_STRAIGHT;

      // 存储节点引用
      let groupBoxMap = {}; // 集群框
      
      const parent = graph.getDefaultParent();

      // 将节点分组 - 从新API数据生成集群组结构
      const nodeGroups = {};

      // 从API获取的节点数据中创建集群
      nodeData.forEach(node => {
        // 提取集群名称
        let clusterName;
        if (node.cluster && node.cluster.length > 0) {
          // 如果有cluster数组，说明是集群
          clusterName = node.productName || node.serviceName || node.groupName || node.ip;
          
          // 创建集群组
          if (!nodeGroups[clusterName]) {
            nodeGroups[clusterName] = {
              groupData: node, // 集群本身的数据
              children: [],    // 子节点数组
              childIPs: {}     // 用于快速检查IP是否已经在子节点中
            };
            
            // 添加子节点 - 直接从cluster数组使用
            if (node.cluster && node.cluster.length > 0) {
              node.cluster.forEach(childNode => {
                if (!nodeGroups[clusterName].childIPs[childNode.ip]) {
                  nodeGroups[clusterName].children.push(childNode);
                  nodeGroups[clusterName].childIPs[childNode.ip] = true;
                }
              });
            }
          }
        } else {
          // 单节点作为独立集群
          clusterName = node.productName || node.serviceName || node.ip;
          
          if (!nodeGroups[clusterName]) {
            nodeGroups[clusterName] = {
              groupData: node,
              children: [],
              childIPs: {}
            };
            
            // 添加自身作为子节点
            const childNode = {
              name: node.productName || node.serviceName || node.ip,
              ip: node.ip,
              type: node.type || 'service',
              status: node.status || 'success',
              img_url: node.img_url,
              problem: node.problem || []
            };
            
            nodeGroups[clusterName].children.push(childNode);
            nodeGroups[clusterName].childIPs[node.ip] = true;
          }
        }
      });

      // 绘制集群和节点
      let groupBoxes = []; // 存储所有集群框对象，用于布局
      
      for (const groupName in nodeGroups) {
        const groupData = nodeGroups[groupName];
        
        // 计算组尺寸
        const childCount = groupData.children.length;
        const maxNodesPerRow = 5;
        const rowCount = Math.ceil(childCount / maxNodesPerRow);
        const groupWidth = Math.max(150, 20 + 130 * Math.min(maxNodesPerRow, childCount));
        const groupHeight = 40 + 100 * rowCount;

        // 确定集群状态
        const groupStatus = groupData.groupData.status || "success";
          
        // 调整集群边框颜色以反映状态
        const groupStrokeColor = getStatusColor(groupStatus);

        // 创建组容器
        const groupBox = graph.insertVertex(
          parent, null, '', 
          Math.random() * 400, Math.random() * 400,
          groupWidth, groupHeight,
          `fillColor=#f5f7fa;strokeColor=${groupStrokeColor};strokeWidth=${groupStatus !== 'success' ? 3 : 2};rounded=1;shadow=1;`
        );
        
        // 设置用户数据
        groupBox.userData = { 
          ...groupData.groupData,
          isGroup: true  // 标记为集群
        };
        
        // 保存到映射中
        const groupIdentifier = groupData.groupData.serverUrl || groupName;
        groupBoxMap[groupIdentifier] = groupBox;
        groupBoxes.push(groupBox);

        // 添加组标签
        const labelBgColor = groupStatus === 'success' ? '#e6f7ff' : 
                           groupStatus === 'danger' ? '#fff2e8' : '#fff7e6';
                           
        const displayName = groupData.groupData.productName || groupData.groupData.serviceName || groupName;
                           
        const label = graph.insertVertex(
          groupBox, null, 
          truncateText(displayName, Math.floor((groupWidth - 20) / 14)),
          10, 5, groupWidth - 20, 25,
          `fontSize=14;fontStyle=1;align=center;verticalAlign=middle;strokeColor=none;fillColor=${labelBgColor};rounded=1;spacing=10;`
        );

        let xOffset = 20;
        let yOffset = 35;
        let rowNodes = 0;

        // 添加子节点
        groupData.children.forEach((node, index) => {
          if (rowNodes >= maxNodesPerRow) {
            xOffset = 20;
            yOffset += 100;
            rowNodes = 0;
          }
          
          // 基于状态设置节点样式
          const nodeStatus = node.status || "success";
          const nodeType = node.type || "service";
          
          // 设置节点样式
          const strokeColor = getStatusColor(nodeStatus);
          const backgroundColor = getStatusBgColor(nodeStatus);
          const iconPath = new URL(`../../../../assets/icons/topology-${nodeType}-${nodeStatus}.svg`, import.meta.url).href
          let style = {
            [mxConstants.STYLE_SHAPE]: mxConstants.SHAPE_LABEL,
            [mxConstants.STYLE_STROKECOLOR]: strokeColor,
            [mxConstants.STYLE_FILLCOLOR]: backgroundColor,
            [mxConstants.STYLE_FONTCOLOR]: "#000000",
            [mxConstants.STYLE_ALIGN]: mxConstants.ALIGN_CENTER,
            [mxConstants.STYLE_VERTICAL_ALIGN]: mxConstants.ALIGN_BOTTOM,
            [mxConstants.STYLE_FONTSIZE]: 12,
            [mxConstants.STYLE_FONTWEIGHT]: "bold",
            [mxConstants.STYLE_IMAGE_ALIGN]: mxConstants.ALIGN_CENTER,
            [mxConstants.STYLE_IMAGE_VERTICAL_ALIGN]: mxConstants.ALIGN_TOP,
            [mxConstants.STYLE_IMAGE]: iconPath,
            [mxConstants.STYLE_IMAGE_WIDTH]: 30,
            [mxConstants.STYLE_IMAGE_HEIGHT]: 30,
            [mxConstants.STYLE_SPACING_TOP]: 20,
            [mxConstants.STYLE_SPACING]: 8,
            [mxConstants.STYLE_ROUNDED]: 1,
            [mxConstants.STYLE_SHADOW]: 1,
            [mxConstants.STYLE_OVERFLOW]: 'visible',
            [mxConstants.STYLE_WHITE_SPACE]: 'wrap'
          };

          const styleName = `${nodeType}-${nodeStatus}-${index}`;
          graph.getStylesheet().putCellStyle(styleName, style);

          // 创建节点，显示子节点的名称或IP
          const displayName = node.name || node.ip || "";
          const vertex = graph.insertVertex(
            groupBox, null,
            truncateText(displayName, 9),
            xOffset, yOffset, 100, 70,
            styleName
          );
          
          vertex.userData = node;
       
          xOffset += 130;
          rowNodes++;
        });
      }

      // 处理链接关系
      linkData.forEach(link => {
        const sourceBox = groupBoxMap[link.node_from_name];
        const targetBox = groupBoxMap[link.node_to_name];
        
        if (sourceBox && targetBox) {
          // 创建集群之间的连接
          graph.insertEdge(
            parent,
            null,
            link.port || "", // 显示端口号
            sourceBox,
            targetBox,
            'rounded=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1890ff;'
          );
        }
      });

      // 应用放射状布局
      const layout = new mxRadialTreeLayout(graph);
      // 设置布局属性以避免节点过大
      layout.autoRadius = true;
      layout.levelDistance = 150;
      layout.nodeDistance = 120;
      
      // 只对集群框应用布局，而不是子节点
      layout.execute(parent, groupBoxes.length > 0 ? groupBoxes[0] : null);
      
      // 自适应并居中
      setTimeout(() => {
        graph.fit();
        graph.center();
        
        // 检查当前比例，如果节点过大则设置合适的缩放
        const currentScale = graph.view.scale;
        if (currentScale > 1.0 && groupBoxes.length < 5) {
          // 如果节点很少且缩放比例大于1，则强制使用较小的缩放比例
          graph.zoomTo(Math.min(currentScale, 0.8));
          graph.center();
        }
      }, 100);
      
      // 允许移动集群，但不影响连线
      graph.isCellMovable = function (cell) {
        return !cell.isEdge();
      };
    } finally {
      model.endUpdate();
    }
  }).catch(error => {
    console.error('获取拓扑数据失败:', error);
  });
}

function getXml() {
  data.exportDialog = true;
  const encoder = new mxCodec();
  const node = encoder.encode(graph.getModel());
  data.xml = new XMLSerializer().serializeToString(node);
  
  // 隐藏tooltip
  tooltip.showTooltip = false;
}

function closeInformation() {
  data.informationVisible = false;
  data.informationDialog = false;
  tooltip.showTooltip = false;
}

function resetGraph() {
  data.informationVisible = false;
  data.informationDialog = false;
  destroyGraph();
  drawTopology();
}

function opneInformation() {
  data.informationVisible = true;
  data.informationDialog = true;
}

function destroyGraph() {
  if (graph) {
    graph.destroy();
    graph = null;
  }
}

// 缩放控制函数
function zoomIn() {
  if (graph) {
    graph.zoomIn();
  }
}

function zoomOut() {
  if (graph) {
    graph.zoomOut();
  }
}

function fitGraph() {
  if (graph) {
    graph.fit();
    graph.center();
  }
}

function showAddBusinessForm() {
  data.addBusinessFormVisible = true;
  // 隐藏tooltip
  tooltip.showTooltip = false;
}

function showEditBusinessForm() {
  if (!data.isNodeSelected || !data.selectBusiness) {
    ElMessage.warning('请先选择一个业务节点');
    return;
  }
  
  // 使用JSON深拷贝确保数据正确传递，避免引用问题
  data.currentBusinessToEdit = JSON.parse(JSON.stringify(data.selectBusiness));
  
  data.editBusinessFormVisible = true;
  
  // 隐藏tooltip
  tooltip.showTooltip = false;
}

function handleEditBusiness(businessData) {
  
  // 构造符合拓扑图数据结构的节点，将clusterNodes转换为cluster
  const updatedNode = {
    ...businessData,
    cluster: businessData.clusterNodes || [],
  };
  
  // 删除多余的字段
  delete updatedNode.clusterNodes;
  
  // 发送请求到后端保存数据
  editBusinessTopology(updatedNode).then(res => {
    if (res && res.status === 200) {
      ElMessage.success(res.message || '业务编辑成功');
      // 清除选中状态
      data.selectBusiness = null;
      data.isNodeSelected = false;
      // 重新加载拓扑图
      resetGraph();
    } else {
      ElMessage.error(res.error || '业务编辑失败');
    }
  }).catch(err => {
    console.error('编辑业务失败:', err);
    ElMessage.error('业务编辑失败');
  });
}

function showDeleteConfirm() {
  if (!data.isNodeSelected || !data.selectBusiness) {
    ElMessage.warning('请先选择一个业务节点');
    return;
  }
  
  // 检查是否是主节点集群（通过比较productName和route.query.item）
  let isMainCluster = false;
  if (route && route.query && route.query.item && data.selectBusiness.productName) {
    isMainCluster = data.selectBusiness.productName === route.query.item;
  }

  if (isMainCluster) {
    ElMessage.error('不能删除主节点集群');
    return;
  }
  
  // 设置当前要删除的业务数据
  data.currentBusinessToDelete = data.selectBusiness;
  data.deleteConfirmVisible = true;
  
  // 隐藏tooltip
  tooltip.showTooltip = false;
}

function handleDeleteBusiness() {
  if (!data.currentBusinessToDelete) {
    ElMessage.error('删除失败：未选择业务');
    return;
  }
  
  // 构造删除请求的参数
  const deleteParams = {
    serverUrl: data.currentBusinessToDelete.serverUrl || '',
    productName: data.currentBusinessToDelete.productName || ''
  };
  
  // 发送请求到后端删除数据
  deleteBusinessTopology(deleteParams).then(res => {
    if (res && res.status === 200) {
      ElMessage.success(res.message || '业务删除成功');
      // 关闭删除确认对话框
      data.deleteConfirmVisible = false;
      // 清除选择状态
      data.selectBusiness = null;
      data.isNodeSelected = false;
      // 重新加载拓扑图
      resetGraph();
    } else {
      ElMessage.error(res.error || '业务删除失败');
    }
  }).catch(err => {
    console.error('删除业务失败:', err);
    ElMessage.error('业务删除失败');
  });
}

function handleAddBusiness(businessData) {
  
  // 构造符合拓扑图数据结构的节点，将clusterNodes转换为cluster
  const newNode = {
    ...businessData,
    cluster: businessData.clusterNodes || [],
  };
  
  // 删除多余的字段
  delete newNode.clusterNodes;
  
  // 发送请求到后端保存数据
  editBusinessTopology(newNode).then(res => {
    if (res && res.status === 200) {
      ElMessage.success(res.message || '业务添加成功');
      // 清除选择状态
      data.selectBusiness = null;
      data.isNodeSelected = false;
      // 重新加载拓扑图
      resetGraph();
    } else {
      ElMessage.error(res.error || '业务添加失败');
    }
  }).catch(err => {
    console.error('添加业务失败:', err);
    ElMessage.error('业务添加失败');
  });
}

</script>

<template>
  <div>
    <page-main>
      <div id="drawioContainer" class="contain"></div>
      
      <!-- 自定义工具提示 -->
      <div id="customTooltip" v-show="tooltip.showTooltip" :style="tooltipStyle">
        <div class="tooltip-title">{{ tooltip.tooltipName }}</div>
        <div class="tooltip-ip">
          <span class="ip-label">IP地址：</span>
          <span class="ip-value">{{ tooltip.tooltipIp || '无IP信息' }}</span>
        </div>
        
        <!-- 额外信息显示 -->
        <div v-if="tooltip.extraInfo && tooltip.extraInfo.length > 0" class="tooltip-extra-info">
          <div v-for="(item, index) in tooltip.extraInfo" :key="index" class="info-item">
            <span class="info-key">{{ item.key }}：</span>
            <span v-if="item.html" class="info-value" v-html="item.value"></span>
            <span v-else class="info-value">{{ item.value }}</span>
          </div>
        </div>
        
        <!-- 问题显示区域 -->
        <div v-if="tooltip.tooltipProblem && tooltip.tooltipProblem.length > 0" class="problem-container">
          <div class="problem-header">检测到 {{ tooltip.tooltipProblem.length }} 个问题：</div>
          <div v-for="(item, index) in tooltip.tooltipProblem" :key="index" class="problem-item">
            <div class="problem-item-header">
              <span 
                class="problem-priority" 
                :style="{ backgroundColor: item && item.priority ? getAlertType(item.priority).color : '#909399' }"
              >
                {{ item && item.priority }}
              </span>
              <span class="problem-time" v-if="item && item.lastchange">{{ formatDateTime(item.lastchange) }}</span>
            </div>
            <div class="problem-description">{{ item && item.description }}</div>
          </div>
        </div>
        <div v-else class="problem-container">
          <div class="problem-header">暂无问题</div>
        </div>
      </div>
      
      <!-- 改进的工具栏 -->
      <div class="tools" :class="{'tools-minimized': data.toolbarMinimized}">
        <div class="tool-header">
        <div class="tool-title">工具栏</div>
          <div class="minimize-button" @click="data.toolbarMinimized = !data.toolbarMinimized">
            {{ data.toolbarMinimized ? '展开' : '收起' }}
      </div>
        </div>
        
        <div v-if="!data.toolbarMinimized" class="tool-content">
          <!-- 缩放控制 -->
          <div class="tool-section">
            <div class="tool-group-title">缩放控制</div>
            <div class="zoom-controls">
              <button class="tool-item" title="放大" @click="zoomIn">
                <i class="zoom-icon">+</i>
              </button>
              <button class="tool-item" title="缩小" @click="zoomOut">
                <i class="zoom-icon">-</i>
              </button>
              <button class="tool-item" title="适应窗口" @click="fitGraph">
                <i class="zoom-icon">适配</i>
              </button>
            </div>
          </div>
          
          <!-- 图例说明 -->
          <div class="tool-section">
            <div class="tool-group-title">状态说明</div>
            <div class="legend-content">
              <div class="legend-item">
                <span class="legend-icon success"></span>正常运行
              </div>
              <div class="legend-item">
                <span class="legend-icon warning"></span>警告
              </div>
              <div class="legend-item">
                <span class="legend-icon danger"></span>故障
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="tool-section">
            <div class="tool-group-title">操作</div>
            <!-- <button class="tool-item" title="新增业务" @click="showAddBusinessForm">
              <i class="el-icon-plus"></i> 新增业务
            </button>
            <button class="tool-item" title="编辑业务" @click="showEditBusinessForm" :disabled="!data.isNodeSelected" :class="{'tool-item-disabled': !data.isNodeSelected}">
              <i class="el-icon-edit"></i> 编辑业务
            </button>
            <button class="tool-item tool-item-danger" title="删除业务" @click="showDeleteConfirm" :disabled="!data.isNodeSelected" :class="{'tool-item-disabled': !data.isNodeSelected}">
              <i class="el-icon-delete"></i> 删除业务
            </button> -->
            <button class="tool-item" title="导出XML" @click="getXml">导出XML</button>
            <button class="tool-item" title="重新加载" @click="resetGraph">刷新拓扑图</button>
          </div>
        </div>
      </div>
      
      <!-- 弹窗 -->
      <el-dialog v-model="data.exportDialog" title="xml信息" width="800">
        <div class="xml-content">
          <pre>{{ data.xml }}</pre>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="data.exportDialog = false">关闭</el-button>
            <el-button type="primary" @click="data.exportDialog = false">复制</el-button>
          </div>
        </template>
      </el-dialog>
      
      <BusinessInformation
        :item="data.selectBusiness"
        v-if="data.informationVisible"
        v-model="data.informationDialog"
        @closeInformation="closeInformation"
        @resetGraph="resetGraph"
      ></BusinessInformation>
      
      <!-- 新增业务表单 -->
      <BusinessForm
        v-model:visible="data.addBusinessFormVisible"
        @submit="handleAddBusiness"
      />
      
      <!-- 编辑业务表单 -->
      <BusinessForm
        v-model:visible="data.editBusinessFormVisible"
        :businessData="data.currentBusinessToEdit"
        @submit="handleEditBusiness"
      />
      
      <!-- 删除确认对话框 -->
      <el-dialog
        v-model="data.deleteConfirmVisible"
        title="确认删除"
        width="400px"
      >
        <div class="delete-confirm-content">
          <i class="el-icon-warning" style="color: #ff4d4f; font-size: 24px; margin-right: 10px;"></i>
          <span>确认删除该业务吗？此操作不可恢复。</span>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="data.deleteConfirmVisible = false">取消</el-button>
            <el-button type="danger" @click="handleDeleteBusiness">确认删除</el-button>
          </div>
        </template>
      </el-dialog>
    </page-main>
  </div>
</template>

<style scoped>
/* 基础容器样式 */
.contain {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 2px solid rgb(221.7, 222.6, 224.4);
  position: relative;
  background-color: #fff;
  background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

.page-main {
  margin: 0px;
  height: 700px;
  position: relative;
}

/* 改进的工具栏样式 */
.tools {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 20px;
  top: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 900;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.tools-minimized {
  min-width: auto;
  width: auto;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eaeaea;
}

  .tool-title {
  font-size: 16px;
    font-weight: bold;
  color: #1a1a1a;
}

.minimize-button {
  cursor: pointer;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(24, 144, 255, 0.1);
}

.minimize-button:hover {
  background-color: rgba(24, 144, 255, 0.2);
}

.tool-section {
  margin-bottom: 15px;
}

.tool-group-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  padding-left: 4px;
  }

  .tool-item {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
    cursor: pointer;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
  text-align: center;
  width: 100%;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tool-item:hover {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #c6e2ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tool-item:active {
  transform: translateY(0);
}

/* 缩放控制样式 */
.zoom-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.zoom-controls .tool-item {
  flex: 1;
  padding: 6px 0;
}

.zoom-icon {
  font-style: normal;
  font-weight: bold;
}

/* 图例样式 */
.legend-content {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
}

.legend-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 8px;
}

.legend-icon.success {
  background-color: #52c41a;
}

.legend-icon.warning {
  background-color: #faad14;
}

.legend-icon.danger {
  background-color: #f5222d;
}

/* 提示框样式 */
#customTooltip {
  position: fixed;
  background-color: rgba(33, 33, 33, 0.9);
  color: #ffffff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  font-family: 'Arial', sans-serif;
  z-index: 9999;
  transition: all 0.3s;
  max-width: 350px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease;
  pointer-events: none; /* 确保不捕获鼠标事件 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.tooltip-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-ip {
  font-size: 14px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 10px;
  border-radius: 4px;
}

.ip-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
}

.ip-value {
  color: #ffffff;
  font-weight: 500;
  text-align: right;
}

/* 问题样式区域 */
.problem-container {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  margin-top: 8px;
}

.problem-header {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  color: #faad14;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.status-success {
  color: #52c41a;
}

.problem-item {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.problem-item:last-child {
  margin-bottom: 0;
}

.problem-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.problem-priority {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.problem-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.priority-disaster {
  background-color: #722ed1;
  color: white;
}

.priority-danger {
  background-color: #f5222d;
  color: white;
}

.priority-warning {
  background-color: #faad14;
  color: white;
}

.priority-info {
  background-color: #1890ff;
  color: white;
}

.priority-default {
  background-color: #8c8c8c;
  color: white;
}

.problem-description {
  font-size: 12px;
  line-height: 1.4;
}

/* XML内容样式 */
.xml-content {
  max-height: 400px;
  overflow: auto;
  background-color: #282c34;
  color: #abb2bf;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Courier New', monospace;
}

.xml-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  padding-top: 16px;
  text-align: right;
}

.tooltip-extra-info {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.info-key {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 5px;
  min-width: 70px;
}

.info-value {
  color: #ffffff;
  font-weight: 500;
  text-align: right;
  max-width: 220px;
  word-break: break-all;
}

/* 状态标签的样式 */
.status-label {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 5px;
}

.status-success {
  background-color: #52c41a;
  color: white;
}

.status-warning {
  background-color: #faad14;
  color: white;
}

.status-danger {
  background-color: #f5222d;
  color: white;
}

.status-unknown {
  background-color: #8c8c8c;
  color: white;
}

/* 新增业务按钮样式 */
.tool-item i {
  margin-right: 4px;
}

.tool-item:has(i) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
}

.tool-item:has(.el-icon-plus) {
  background-color: #f0f5ff;
  color: #1890ff;
  border-color: #d4e6fd;
}

.tool-item:has(.el-icon-plus):hover {
  background-color: #e6f0ff;
  color: #1890ff;
  border-color: #a8c4f5;
}

/* 添加编辑按钮样式 */
.tool-item:has(.el-icon-edit) {
  background-color: #f6ffed;
  color: #52c41a;
  border-color: #d9f7be;
}

.tool-item:has(.el-icon-edit):hover {
  background-color: #eaffe8;
  color: #52c41a;
  border-color: #b7eb8f;
}

/* 添加删除按钮样式 */
.tool-item-danger {
  background-color: #fff2f0;
  color: #ff4d4f;
  border-color: #ffccc7;
}

.tool-item-danger:hover {
  background-color: #fff1f0;
  color: #ff4d4f;
  border-color: #ff7875;
}

/* 删除确认对话框样式 */
.delete-confirm-content {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

/* 添加禁用按钮的样式 */
.tool-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.tool-item-disabled:hover {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  border-color: #dcdfe6 !important;
  transform: none !important;
  box-shadow: none !important;
}

.tool-item-danger.tool-item-disabled:hover {
  background-color: #fff2f0 !important;
  color: #ff4d4f !important;
  border-color: #ffccc7 !important;
}
</style>
