<template>
  <div class="nginx-analysis-container">
    <!-- 顶部导航与返回按钮 -->
    <div class="top-bar">
      <div class="left-section">
        <el-button type="primary" plain @click="goBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon> 返回配置
        </el-button>
        <div class="node-basic-info">
          <span class="node-title">{{ nodeData.businessName }}</span>
          <el-tag v-if="!nodeData.isCluster" :type="tagType" effect="light" class="status-tag">{{ nodeStatusText }}</el-tag>
          <el-tag v-else type="primary" effect="plain" class="status-tag">集群</el-tag>
        </div>
      </div>
      <div class="node-details">
        <div class="detail-item">
          <span class="detail-label">IP:</span>
          <span class="detail-value">{{ nodeData.ip }}</span>
        </div>
        <div class="detail-item" v-if="nodeData.ports">
          <span class="detail-label">端口:</span>
          <span class="detail-value">{{ nodeData.ports }}</span>
        </div>
      </div>
    </div>

    <!-- 全局筛选区域 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="response-time-filter">
          <div class="filter-label">响应时间筛选：</div>
          <div class="filter-options">
            <el-radio-group v-model="responseTimePreset" size="small" @change="handleResponseTimePresetChange">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="slow">慢 > 1000ms</el-radio-button>
              <el-radio-button label="medium">中 500-1000ms</el-radio-button>
              <el-radio-button label="fast">快 < 500ms</el-radio-button>
            </el-radio-group>
            <div class="custom-range" v-if="responseTimePreset === 'custom'">
              <el-input-number v-model="responseTimeMin" :min="0" :max="responseTimeMax" size="small" placeholder="最小值" />
              <span class="range-separator">-</span>
              <el-input-number v-model="responseTimeMax" :min="responseTimeMin" size="small" placeholder="最大值" />
              <span class="range-unit">ms</span>
            </div>
            <el-button size="small" type="primary" plain @click="switchToCustomRange" class="custom-btn">
              自定义
            </el-button>
          </div>
        </div>
        
        <el-input v-model="ipSearchInput" placeholder="请求IP筛选" clearable 
                 @input="handleIpSearchChange" @clear="clearIpSearch" class="ip-input">
          <template #prefix>
            <el-icon class="el-input__icon"><svg-icon name="ep:monitor" /></el-icon>
          </template>
        </el-input>

        <el-input v-model="statusCodeInput" placeholder="状态码筛选" clearable 
                 @input="handleStatusCodeChange" @clear="clearStatusCodeSearch" class="status-input">
          <template #prefix>
            <el-icon class="el-input__icon"><svg-icon name="ep:warning" /></el-icon>
          </template>
        </el-input>
        
        <el-input v-model="apiEndpointInput" placeholder="API端点筛选" clearable 
                 @input="handleApiEndpointChange" @clear="clearApiEndpointSearch" class="api-input">
          <template #prefix>
            <el-icon class="el-input__icon"><svg-icon name="ep:link" /></el-icon>
          </template>
        </el-input>

        <div class="filter-actions">
          <el-button type="primary" @click="applyFilters" :disabled="!filterChanged">
            {{ filterChanged ? '应用筛选' : '筛选' }}
          </el-button>
          <el-button v-if="hasFilters" @click="clearFilters" plain>清除筛选</el-button>
        </div>
      </div>
    </div>
 <!-- IP响应时间分析 -->
    <div class="dashboard-row">
        <el-card shadow="hover" class="chart-card" style="grid-column: span 2;">
          <template #header>
            <div class="card-header">
              <span>响应时间趋势</span>
            </div>
          </template>
          <div class="chart-container" ref="ipResponseTimeChartContainer"></div>
        </el-card>
    </div>
    <!-- 状态码与API端点分析 -->
    <div class="dashboard-row">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>状态码分布</span>
            </div>
          </template>
          <div class="chart-container" ref="statusCodeChartContainer"></div>
        </el-card>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>常用API端点</span>
            </div>
          </template>
          <div class="chart-container" id="apiEndpointsChart"></div>
        </el-card>
    </div>

    <!-- Nginx响应分析 -->
    <div class="dashboard-row">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Nginx应用响应时间分布</span>
            </div>
          </template>
          <div class="chart-container" ref="nginxResponseTimeChartContainer"></div>
        </el-card>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Nginx应用响应次数</span>
            </div>
          </template>
          <div class="chart-container" ref="nginxResponseCountChartContainer"></div>
        </el-card>
    </div>

    <!-- API响应分析 -->
    <div class="dashboard-row">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>API平均响应时间</span>
            </div>
          </template>
          <div class="chart-container" ref="apiResponseTimeChartContainer"></div>
        </el-card>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Nginx调用响应大小</span>
            </div>
          </template>
          <div class="chart-container" ref="responseSizeChartContainer"></div>
        </el-card>
    </div

    <!-- 业务健康度与客户端分析 -->
    <div class="dashboard-row">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>业务健康度分析</span>
            </div>
          </template>
          <div class="chart-container" ref="businessHealthChartContainer"></div>
        </el-card>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>客户端分析</span>
            <el-radio-group v-model="clientAnalysisDimension" size="small" class="client-analysis-tabs">
                <el-radio-button label="ip">IP分布</el-radio-button>
                <el-radio-button label="os">操作系统</el-radio-button>
                <el-radio-button label="browser">浏览器</el-radio-button>
                <el-radio-button label="browserVersion">浏览器版本</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" ref="clientAnalysisChartContainer"></div>
        </el-card>
    </div>

   

    <!-- 日志区域 -->
        <el-card shadow="hover" class="log-card">
          <template #header>
            <div class="card-header">
          <div class="header-content">
                <span>Nginx日志</span>
            <el-radio-group v-model="activeLogTab" size="small" @change="handleLogTabChange" class="log-tabs">
                  <el-radio-button label="access">访问日志</el-radio-button>
                  <el-radio-button label="error">错误日志</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <!-- 访问日志表格 -->
          <el-table 
            v-if="activeLogTab === 'access'"
            :data="accessLogData" 
            style="width: 100%" 
            border 
            stripe 
            highlight-current-row 
            v-loading="loading">
            <el-table-column prop="@timestamp" label="时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row['@timestamp']) }}
              </template>
            </el-table-column>
        <el-table-column prop="source_ip" label="请求IP" width="140">
          <template #default="scope">
            <el-tag size="small" effect="plain" type="info">{{ scope.row.source_ip }}</el-tag>
          </template>
        </el-table-column>
            <el-table-column prop="http.request.method" label="请求方法" width="100">
              <template #default="scope">
                <el-tag size="small" :type="
                  scope.row['http.request.method'] === 'GET' ? 'success' :
                  scope.row['http.request.method'] === 'POST' ? 'warning' :
                  scope.row['http.request.method'] === 'PUT' ? 'info' :
                  scope.row['http.request.method'] === 'DELETE' ? 'danger' : 'info'
                ">
                  {{ scope.row['http.request.method'] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="url.original" label="请求路径" min-width="200" show-overflow-tooltip />
            <el-table-column prop="http.response.status_code" label="状态码" width="100">
          <template #default="scope">
                <el-tag size="small" :type="
                  scope.row['http.response.status_code'].toString().startsWith('2') ? 'success' :
                  scope.row['http.response.status_code'].toString().startsWith('3') ? 'warning' :
                  scope.row['http.response.status_code'].toString().startsWith('4') ? 'danger' :
                  scope.row['http.response.status_code'].toString().startsWith('5') ? 'danger' : 'info'
                ">
                  {{ scope.row['http.response.status_code'] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="http.response.body.bytes" label="响应大小" width="120">
              <template #default="scope">
                {{ (scope.row['http.response.body.bytes'] / 1024).toFixed(2) }} KB
              </template>
            </el-table-column>
            <el-table-column prop="http.response.time" label="响应时间" width="120">
              <template #default="scope">
                {{ scope.row['http.response.time'].toFixed(3) }} s
          </template>
        </el-table-column>
          </el-table>
          
          <!-- 错误日志表格 -->
          <el-table 
            v-if="activeLogTab === 'error'"
            :data="errorLogData" 
            style="width: 100%" 
            border 
            stripe 
            highlight-current-row 
            v-loading="loading">
            <el-table-column prop="@timestamp" label="时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row['@timestamp']) }}
              </template>
            </el-table-column>
            <el-table-column prop="log.level" label="级别" width="100">
              <template #default="scope">
                <el-tag :type="
                  scope.row['log.level'] === 'ERROR' ? 'danger' :
                  scope.row['log.level'] === 'WARNING' ? 'warning' :
                  scope.row['log.level'] === 'CRITICAL' ? 'danger' :
                  scope.row['log.level'] === 'ALERT' ? 'danger' : 'info'
                ">
                  {{ scope.row['log.level'] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="error.message" label="错误信息" min-width="200" show-overflow-tooltip />
          </el-table>
          
          <!-- 分页 -->
          <el-pagination 
            :current-page="pagination.page" 
            :total="pagination.total" 
            :page-size="pagination.size"
            :page-sizes="pagination.sizes" 
            :layout="pagination.layout" 
            :hide-on-single-page="false" 
            class="pagination-container"
            background 
            @size-change="handleSizeChange" 
            @current-change="handleCurrentChange" />
        </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft, Search } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import api from '@/plugins/axios';
import useDateTimeStore from '@/store/modules/datetime';
import { getNginxLogs,getNginxAnalysis } from '@/api/modules/nginx_management';
import { usePagination } from "@/utils/composables";
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { Column,G2 } from '@antv/g2plot'; // 导入G2Plot的Column组件

// 配置dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);

// 添加通用的时间格式化函数
function formatTime(time: string, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return '';
  try {
    return dayjs(time).format(format);
  } catch (error) {
    return time;
  }
}

// 添加图表时间格式化函数
function formatChartTime(time: string): string {
  if (!time) return '';
  try {
    return dayjs(time).format('HH:mm');
  } catch (error) {
    return time;
  }
}

// 添加 EchartsSeriesItem 类型
interface EchartsSeriesItem {
  name: string;
  type: string;
  smooth?: boolean;
  symbol?: string;
  symbolSize?: number;
  sampling?: string;
  data: any[];
  animationDuration?: number;
  lineStyle?: {
    width: number;
  };
  emphasis?: {
    focus?: string;
    itemStyle?: any;
  };
  itemStyle?: any;
  barWidth?: string;
  label?: any;
  markLine?: any; // 添加markLine属性
  areaStyle?: any; // 添加areaStyle属性以支持面积图
  connectNulls?: boolean; // 添加connectNulls属性
}

// 定义类型
interface StatusCode {
  code: string;
  count: number;
  percentage: number;
}

interface ApiEndpoint {
  name: string;
  count: number;
  avgTime: string;
  errorRate: string;
}



// 访问日志接口定义
interface AccessLogEntry {
  '@timestamp': string;
  'url.original': string;
  'http.request.method': string;
  'http.response.time': number;
  'http.response.status_code': number;
  'source_ip': string;
  'http.response.body.bytes': number;
}

// 错误日志接口定义
interface ErrorLogEntry {
  '@timestamp': string;
  'log.level': string;
  'error.message': string;
}

interface TimeSeriesItem {
  time: string;
  value: number;
}


// API 响应时间项
interface ApiResponseItem {
  name: string;
  value: number;
  calls: number;
}

// 添加API响应时间时序数据接口
interface ApiTimeSeriesItem {
  api: string;  // API名称
  times: TimeSeriesItem[];  // 该API的时序数据
}


const router = useRouter();
const route = useRoute();

// 使用时间store
const dateTimeStore = useDateTimeStore();

// 组件引用
const statusCodeChartContainer = ref<HTMLElement | null>(null);
let statusCodeChart: echarts.ECharts | null = null;

// 添加常用API端点图表引用
let apiEndpointsChart: echarts.ECharts | null = null;

// 新增图表相关
const nginxResponseTimeChartContainer = ref<HTMLElement | null>(null);
const nginxResponseCountChartContainer = ref<HTMLElement | null>(null);
const apiResponseTimeChartContainer = ref<HTMLElement | null>(null);
const responseSizeChartContainer = ref<HTMLElement | null>(null);
const businessHealthChartContainer = ref<HTMLElement | null>(null);
const clientAnalysisChartContainer = ref<HTMLElement | null>(null);
let nginxResponseTimeChart: echarts.ECharts | null = null;
let nginxResponseCountChart: echarts.ECharts | null = null;
let apiResponseTimeChart: echarts.ECharts | null = null;
let responseSizeChart: echarts.ECharts | null = null;
let businessHealthChart: echarts.ECharts | null = null;
let clientAnalysisChart: echarts.ECharts | null = null;
// 添加G2Plot图表实例
let nginxResponseTimeG2Chart: Column | null = null;

// 节点数据 - 简化了结构，只保留必要的字段
const nodeData = reactive({
  ip: '',
  businessName: '',
  businessType: '',
  businessStatus: 'none',
  ports: '',
  isCluster: false // 添加是否为集群的标志
});

// 日志相关
const activeLogTab = ref('access'); // 默认显示访问日志
const accessLogData = ref<AccessLogEntry[]>([]);
const errorLogData = ref<ErrorLogEntry[]>([]);

// 原先的变量声明
const responseTimeInput = ref(''); // 响应时间输入框值
// 添加新的响应时间筛选相关变量
const responseTimePreset = ref('all'); // 预设选项：all, slow, medium, fast, custom
const responseTimeMin = ref(0); // 自定义范围最小值
const responseTimeMax = ref(1000); // 自定义范围最大值
const ipSearchInput = ref(''); // IP搜索输入框的值
const statusCodeInput = ref(''); // 状态码搜索输入框的值
const apiEndpointInput = ref(''); // API端点搜索输入框的值
const loading = ref(false); // 加载状态

// 使用usePagination钩子来管理分页
const { pagination, getParams, onSizeChange, onCurrentChange } = usePagination();

// 添加一个新的响应式变量来跟踪筛选条件是否变更
const filterChanged = ref(false);

// 添加防止重复筛选变量
const isFilterChanging = ref(false);

// 添加日志筛选条件
const logFilters = reactive({
  statusCode: '', // 状态码筛选
  apiEndpoint: '', // API端点筛选
  sourceIp: '', // 源IP筛选
  responseTime: '' // 响应时间筛选
});

// 状态码数据
const statusCodeData = ref<StatusCode[]>([]);

// 响应大小数据
const apiEndpointData = ref<ApiEndpoint[]>([]);

// 新增图表数据
const nginxResponseTimeData = reactive({
  timestamps: [] as string[],
  counts: [] as number[]
});



const apiCallTimeData = reactive({
  timestamps: [] as string[],
  callCounts: [] as number[],
  responseTimes: [] as number[]
});

// API响应时间明细(按API分类)
const apiResponseTimeByName = reactive({} as Record<string, number[]>);

// 新增API时序数据数组 - 扁平化结构
const apiResponseTimeSeries = ref<ApiTimeSeriesItem[]>([]);



// 新增业务健康度和客户端分析数据
interface BusinessHealthItem {
  moduleName: string;  // 业务模块名称
  healthScore: number; // 健康分值 0-100
  errorRate: number;   // 错误率
  avgResponseTime: number; // 平均响应时间
  callVolume: number;  // 调用量
  status: 'good' | 'warning' | 'danger'; // 状态
}

interface ClientAnalysisItem {
  clientType: string;  // 客户端类型
  percentage: number;  // 占比百分比
  count: number;       // 请求数量
  avgResponseTime: number; // 平均响应时间
  os?: string;         // 操作系统
  browser?: string;    // 浏览器
  browserVersion?: string; // 浏览器版本
}

// 业务健康度数据
const businessHealthData = ref<BusinessHealthItem[]>([]);

// 客户端分析数据
const clientAnalysisData = ref<ClientAnalysisItem[]>([]);

// 源IP数据
const sourceIpData = reactive({
  ips: [] as string[],
  responseTimeByIp: {} as Record<string, TimeSeriesItem[]>,
  responseCountTimeSeriesByIp: {} as Record<string, TimeSeriesItem[]>,
  responseSizeTimeSeriesByIp: {} as Record<string, TimeSeriesItem[]>,
  responseCountByIp: {} as Record<string, number>,
  responseSizeByIp: {} as Record<string, { distribution: any[] }>
});

// 计算属性
const tagType = computed(() => {
  if (nodeData.businessStatus === 'success') return 'success';
  if (nodeData.businessStatus === 'danger') return 'warning';
  return 'info';
});

const nodeStatusText = computed(() => {
  if (nodeData.businessStatus === 'success') return '正常';
  if (nodeData.businessStatus === 'danger') return '警告';
  return '未知';
});

// 添加响应数据变量
const resData = reactive({
  businessImpact: null as any,
  avargeResponse: [] as { service: string; avgTime: string; calls: number; timeSeries?: TimeSeriesItem[] }[],
  // 添加API返回数据的其他属性
  responseCount: [] as any[],
  responseSize: [] as any[],
  statusCode: [] as any[],
  api: [] as any[],
  ipResponseTime: [] as any[],
  responseTimeByIp: [] as any[], // 添加responseTimeByIp属性
  businessHealth: [] as any[],
  clientAnalysis: [] as any[],
  basic: {} as any
});

// 方法
function goBack() {
  router.back();
}

// 添加自定义的时间戳转换函数，替代 getUTCTimestampWithOffset
function convertTimestampToSeconds(timestamp) {
  // 检查时间戳是否为数字类型
  let timeValue = timestamp;
  if (typeof timestamp !== 'number') {
    // 尝试转换为数字
    timeValue = Number(timestamp);
  }
  
  // 检查时间戳是否为毫秒级（13位）
  if (timeValue > 1000000000000) {
    // 将毫秒转换为秒
    timeValue = Math.floor(timeValue / 1000);
  }
  return timeValue;
}

// 获取日志数据 - 使用新的接口和分页方式
function fetchLogs() {
  loading.value = true;
  
  // 准备请求参数，确保时间戳格式正确
  const params = {
    start_time: convertTimestampToSeconds(dateTimeStore.begin),
    end_time: convertTimestampToSeconds(dateTimeStore.over),
    ip: nodeData.ip, // 使用节点IP
    source_ip: logFilters.sourceIp, // 添加源IP筛选
    log_type: activeLogTab.value,
    response_time: logFilters.responseTime, // 使用响应时间筛选
    status: logFilters.statusCode,
    api_path: logFilters.apiEndpoint,
    page: pagination.value.page,
    page_size: pagination.value.size,
    ports: nodeData.ports,
    type: route.query.type,
  };
  
  // 调用日志接口
  getNginxLogs(params)
  // api.post('/nginx/log', JSON.stringify(params), { baseURL: '/mock/' })
    .then(res => {
      // 正确地从res.data中获取属性
      const responseData = res.data;
      
      // @ts-ignore 忽略类型错误
        // 根据日志类型设置数据
        if (activeLogTab.value === 'access') {
          // @ts-ignore 忽略类型错误
          accessLogData.value = responseData.data || [];
        } else {
          // @ts-ignore 忽略类型错误
          errorLogData.value = responseData.data || [];
        }
        
        // 更新分页信息
        // @ts-ignore 忽略类型错误
        pagination.value.total = responseData.total || 0;
        
        // 记录当前页码和分页大小，同步后端返回的值
        // @ts-ignore 忽略类型错误
        pagination.value.page = responseData.page || pagination.value.page;
        // @ts-ignore 忽略类型错误
        pagination.value.size = responseData.page_size || pagination.value.size;
    
    })
    .catch(error => {
      ElMessage.error('获取日志数据失败');
    })
    .finally(() => {
      loading.value = false;
    });
}

// 处理日志类型切换
function handleLogTabChange() {
  pagination.value.page = 1;
  fetchLogs(); // 重新获取日志
}

// 处理分页大小变化
function handleSizeChange(size: number): void {
  onSizeChange(size).then(() => {
    fetchLogs();
  });
}

// 处理页码变化
function handleCurrentChange(page: number): void {
  onCurrentChange(page).then(() => {
    fetchLogs();
  });
}





// 处理IP搜索变化
function handleIpSearchChange() {
  filterChanged.value = true;
}

// 处理状态码变化
function handleStatusCodeChange() {
  filterChanged.value = true;
}

// 处理API端点变化
function handleApiEndpointChange() {
  filterChanged.value = true;
}

// 切换到自定义范围的函数
function switchToCustomRange() {
  responseTimePreset.value = 'custom';
  filterChanged.value = true;
}

// 处理预设响应时间变更
function handleResponseTimePresetChange() {
  // 根据选择的预设值构建响应时间筛选条件
  switch(responseTimePreset.value) {
    case 'slow':
      responseTimeInput.value = '>1000';
      break;
    case 'medium':
      responseTimeInput.value = '500-1000';
      break;
    case 'fast':
      responseTimeInput.value = '<500';
      break;
    case 'custom':
      responseTimeInput.value = `${responseTimeMin.value}-${responseTimeMax.value}`;
      break;
    default: // 'all'
      responseTimeInput.value = '';
      break;
  }
  
  filterChanged.value = true;
}

// 修改应用筛选条件的函数
function applyFilters() {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
    // 如果是自定义范围，构建响应时间筛选文本
    if (responseTimePreset.value === 'custom') {
      responseTimeInput.value = `${responseTimeMin.value}-${responseTimeMax.value}`;
    }
    
    logFilters.sourceIp = ipSearchInput.value;
    logFilters.responseTime = responseTimeInput.value;
  logFilters.statusCode = statusCodeInput.value;
  logFilters.apiEndpoint = apiEndpointInput.value;
  pagination.value.page = 1;
  
    initPageData();
    filterChanged.value = false;
  } catch (error) {
    ElMessage.error('应用筛选条件失败');
  } finally {
    setTimeout(() => {
      isFilterChanging.value = false;
    }, 300);
  }
}

// 修改清除筛选函数
function clearFilters() {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
    responseTimeInput.value = '';
    responseTimePreset.value = 'all';
    ipSearchInput.value = '';
    statusCodeInput.value = '';
    apiEndpointInput.value = '';
    
    logFilters.responseTime = '';
    logFilters.sourceIp = '';
    logFilters.statusCode = '';
    logFilters.apiEndpoint = '';
    
    pagination.value.page = 1;
    initPageData();
  filterChanged.value = false;
  
    ElMessage.success('已清除所有筛选条件');
  } catch (error) {
    ElMessage.error('清除筛选条件失败');
  } finally {
  setTimeout(() => {
    isFilterChanging.value = false;
  }, 300);
  }
}



function clearIpSearch() {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
  ipSearchInput.value = '';
    logFilters.sourceIp = ''; // 修改为sourceIp
  pagination.value.page = 1;
    initPageData();
  filterChanged.value = false;
  } catch (error) {
    ElMessage.error('清除源IP筛选条件失败');
  } finally {
  setTimeout(() => {
    isFilterChanging.value = false;
  }, 300);
  }
}

function clearStatusCodeSearch() {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
  statusCodeInput.value = '';
  logFilters.statusCode = '';
  pagination.value.page = 1;
    initPageData();
  filterChanged.value = false;
  } catch (error) {
    ElMessage.error('清除状态码筛选条件失败');
  } finally {
  setTimeout(() => {
    isFilterChanging.value = false;
  }, 300);
  }
}

function clearApiEndpointSearch() {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
  apiEndpointInput.value = '';
  logFilters.apiEndpoint = '';
  pagination.value.page = 1;
    initPageData();
  filterChanged.value = false;
  } catch (error) {
    ElMessage.error('清除API端点筛选条件失败');
  } finally {
  setTimeout(() => {
    isFilterChanging.value = false;
  }, 300);
  }
}

// 计算是否有筛选条件
const hasFilters = computed(() => {
  return logFilters.statusCode !== '' || logFilters.apiEndpoint !== '' || logFilters.sourceIp !== '' || logFilters.responseTime !== '';
});



// 修改监听筛选条件变化
const isFromBrush = ref(false);

watch(logFilters, (newFilters) => {
  if (isFilterChanging.value || isFromBrush.value) return;
  
  if (newFilters.sourceIp !== ipSearchInput.value) {
    ipSearchInput.value = newFilters.sourceIp;
  }
  if (newFilters.statusCode !== statusCodeInput.value) {
    statusCodeInput.value = newFilters.statusCode;
  }
  if (newFilters.apiEndpoint !== apiEndpointInput.value) {
    apiEndpointInput.value = newFilters.apiEndpoint;
  }
  if (newFilters.responseTime !== responseTimeInput.value) {
    responseTimeInput.value = newFilters.responseTime;
  }
  
  pagination.value.page = 1;
  initPageData();
}, { deep: true });

// 添加全局loading状态
// const globalLoading = ref(true);

// 修改初始化页面数据函数，移除全局loading相关代码
async function initPageData() {
  // 从路由参数中获取数据
  const { ip, name, status, ports, type } = route.query;
  if (!ip) {
    ElMessage.error('未提供有效的节点IP');
    return;
  }
  
  // 设置节点数据
  nodeData.ip = String(ip);
  nodeData.businessName = decodeURIComponent(String(name || ''));
  nodeData.businessStatus = String(status || 'success');
  nodeData.ports = String(ports || '');
  nodeData.isCluster = type === 'true'; // 根据type参数设置是否为集群
  
  try {
    loading.value = true;
    
    // 获取UTC时间戳，确保两个接口使用相同的时间格式
    const startTime = convertTimestampToSeconds(dateTimeStore.begin);
    const endTime = convertTimestampToSeconds(dateTimeStore.over);
    
    // 添加 type 参数到请求参数
    const analysisParams = {
      ip: nodeData.ip,
      start_time: startTime,
      end_time: endTime,
      status: logFilters.statusCode,
      api_path: logFilters.apiEndpoint,
      source_ip: logFilters.sourceIp,
      response_time: logFilters.responseTime,
      ports: nodeData.ports,
      type: route.query.type // 添加 type 参数
    };
    
    // 准备日志接口参数
    const logParams = {
      start_time: startTime,
      end_time: endTime,
      ip: nodeData.ip,
      source_ip: logFilters.sourceIp,
      log_type: activeLogTab.value,
      response_time: logFilters.responseTime, // 使用响应时间而不是查询
      status: logFilters.statusCode,
      api_path: logFilters.apiEndpoint,
      page: pagination.value.page,
      page_size: pagination.value.size,
      ports: nodeData.ports,
      type: route.query.type // 添加 type 参数
    };
    // 并行请求数据
    const [analysisRes, logsRes] = await Promise.all([
      getNginxAnalysis(analysisParams),getNginxLogs(logParams)
      // api.post('/nginx/analysis', JSON.stringify(analysisParams), { baseURL: '/mock/' }),
      // api.post('/nginx/log', JSON.stringify(logParams), { baseURL: '/mock/' })
    ]);
    
    // 处理分析数据
    const data = analysisRes.data;
    
    // 更新状态码数据 - 直接使用新接口格式，确保响应式更新
    statusCodeData.value = Array.isArray(data.statusCode) ? [...data.statusCode] : [];

    // 更新API端点数据 - 直接使用新接口格式，确保响应式更新
    apiEndpointData.value = Array.isArray(data.api) ? [...data.api] : [];
    
    // departmentData 已不再使用，移除多余代码
    
    // 更新源IP数据 - 从新接口格式中提取
    if (data.responseTimes) {
      // 提取IP列表
      sourceIpData.ips = Object.keys(data.responseTimes);
      
      // 适配时间字段名称变更 
      Object.keys(data.responseTimes).forEach(ip => {
        if (data.responseTimes[ip] && Array.isArray(data.responseTimes[ip])) {
          // 将name字段转换为time字段
          sourceIpData.responseTimeByIp[ip] = data.responseTimes[ip].map(item => ({
            time: item.name || item.time, // 兼容处理
            value: item.value
          }));
        }
      });
    }
      
    // 处理扁平格式的IP响应时间数据
    if (data.ipResponseTime && Array.isArray(data.ipResponseTime)) {
      // 重新组织数据结构为按IP分组
      const ipResponseTime: Record<string, TimeSeriesItem[]> = {};
      
      data.ipResponseTime.forEach(item => {
        if (!ipResponseTime[item.ip]) {
          ipResponseTime[item.ip] = [];
        }
        
        ipResponseTime[item.ip].push({
          time: item.time,
          value: item.value
        });
      });
      
      // 更新sourceIpData中的数据
      if (Object.keys(ipResponseTime).length > 0) {
        sourceIpData.ips = [...new Set([...sourceIpData.ips, ...Object.keys(ipResponseTime)])];
        sourceIpData.responseTimeByIp = {...sourceIpData.responseTimeByIp, ...ipResponseTime};
      }
    }
    
    // 处理IP响应次数数据
    if (data.ipResponseCount && Array.isArray(data.ipResponseCount)) {
      data.ipResponseCount.forEach(item => {
        sourceIpData.responseCountByIp[item.ip] = item.count;
      });
      
      // 确保ips列表包含所有IP
      sourceIpData.ips = [...new Set([...sourceIpData.ips, ...Object.keys(sourceIpData.responseCountByIp)])];
    }
    
    // 处理IP响应次数时间序列数据
    if (data.ipResponseCountTimeSeries && Array.isArray(data.ipResponseCountTimeSeries)) {
      // 重新组织数据结构为按IP分组
      const ipResponseCountTimeSeries: Record<string, TimeSeriesItem[]> = {};
      
      data.ipResponseCountTimeSeries.forEach(item => {
        if (!ipResponseCountTimeSeries[item.ip]) {
          ipResponseCountTimeSeries[item.ip] = [];
        }
        
        ipResponseCountTimeSeries[item.ip].push({
          time: item.time,
          value: item.value
        });
      });
      
      // 更新sourceIpData中的数据
      sourceIpData.responseCountTimeSeriesByIp = ipResponseCountTimeSeries;
      
      // 确保ips列表包含所有IP
      sourceIpData.ips = [...new Set([...sourceIpData.ips, ...Object.keys(ipResponseCountTimeSeries)])];
    }
    
    // 处理IP响应大小分布数据
    if (data.ipResponseSizeDistribution && Array.isArray(data.ipResponseSizeDistribution)) {
      // 重新组织数据结构为按IP分组
      const ipResponseSizeDistribution: Record<string, any> = {};
      
      data.ipResponseSizeDistribution.forEach(item => {
        if (!ipResponseSizeDistribution[item.ip]) {
          ipResponseSizeDistribution[item.ip] = {
            distribution: []
          };
        }
        
        ipResponseSizeDistribution[item.ip].distribution.push({
          range: item.range,
          count: item.count
        });
      });
      
      // 更新sourceIpData中的数据
      sourceIpData.responseSizeByIp = ipResponseSizeDistribution;
      
      // 确保ips列表包含所有IP
      sourceIpData.ips = [...new Set([...sourceIpData.ips, ...Object.keys(ipResponseSizeDistribution)])];
    }
    
    // 处理IP响应大小时间序列数据
    if (data.ipResponseSizeTimeSeries && Array.isArray(data.ipResponseSizeTimeSeries)) {
      // 重新组织数据结构为按IP分组
      const ipResponseSizeTimeSeries: Record<string, TimeSeriesItem[]> = {};
      
      data.ipResponseSizeTimeSeries.forEach(item => {
        if (!ipResponseSizeTimeSeries[item.ip]) {
          ipResponseSizeTimeSeries[item.ip] = [];
        }
        
        ipResponseSizeTimeSeries[item.ip].push({
          time: item.time,
          value: item.value
        });
      });
      
      // 更新sourceIpData中的数据
      sourceIpData.responseSizeTimeSeriesByIp = ipResponseSizeTimeSeries;
      
      // 确保ips列表包含所有IP
      sourceIpData.ips = [...new Set([...sourceIpData.ips, ...Object.keys(ipResponseSizeTimeSeries)])];
    }
    
    // 更新响应时间数据
    if (data.responseTime) {
      nginxResponseTimeData.timestamps = data.responseTime.map(item => item.name);
      nginxResponseTimeData.counts = data.responseTime.map(item => item.value);
    }
    
    // 为API调用时间创建数据
    apiCallTimeData.timestamps = nginxResponseTimeData.timestamps;
    apiCallTimeData.callCounts = nginxResponseTimeData.counts.map(val => Math.floor(val * 1.5));
    apiCallTimeData.responseTimes = nginxResponseTimeData.counts.map(val => Math.floor(val * 0.8));
    
    // 更新API响应时间明细 - 使用avargeResponse数据
    if (data.avargeResponse) {
      resData.avargeResponse = data.avargeResponse;  // 保存原始数据
      
      // 创建扁平化的API时序数据结构
      apiResponseTimeSeries.value = [];
      
      data.avargeResponse.forEach(item => {
        const apiName = item.service;
        // 使用parseFloat替代parseInt，确保小数部分不会丢失
        const avgTime = parseFloat(item.avgTime || "0");
        
        // 使用服务器返回的时序数据
        if (item.timeSeries && Array.isArray(item.timeSeries)) {
          // 确保时序数据中的值不为0
          const validTimeSeries = item.timeSeries.map(ts => ({
            time: ts.time,
            value: parseFloat(ts.value) || avgTime || 0.1 // 如果值为0，使用平均值或默认最小值
          }));
          
          // 保存到扁平化数据结构
          apiResponseTimeSeries.value.push({
            api: apiName,
            times: validTimeSeries
          });
          
          // 同时保持原来的结构以兼容现有代码
          apiResponseTimeByName[apiName] = validTimeSeries.map(ts => ts.value);
        } else {
          // 如果没有时序数据，则创建基于平均值的时间序列 - 使用固定数据模式
          // 确保avgTime有一个有效值
          const effectiveAvgTime = avgTime || 0.1; // 默认最小值为0.1ms
          
          const timeSeries: TimeSeriesItem[] = Array(24).fill(0).map((_, i) => {
            // 生成一个有规律的数据，随时间变化
            const hourFactor = i < 12 ? 1 + i * 0.05 : 1 + (24 - i) * 0.05;
            return {
              time: `${i.toString().padStart(2, '0')}:00`,
              value: Math.max(0.1, effectiveAvgTime * hourFactor) // 确保值大于0
            };
          });
          
          // 保存到扁平化数据结构
          apiResponseTimeSeries.value.push({
            api: apiName,
            times: timeSeries
          });
          
          // 同时保持原来的结构以兼容现有代码
          apiResponseTimeByName[apiName] = Array(24).fill(0).map((_, i) => {
            const hourFactor = i < 12 ? 1 + i * 0.05 : 1 + (24 - i) * 0.05;
            return Math.max(0.1, effectiveAvgTime * hourFactor); // 确保值大于0
          });
        }
      });
    }
    
    // 处理新增的业务健康度数据，确保响应式更新
    businessHealthData.value = Array.isArray(data.businessHealth) ? [...data.businessHealth] : [];

    // 处理新增的客户端分析数据，确保响应式更新
    clientAnalysisData.value = Array.isArray(data.clientAnalysis) ? [...data.clientAnalysis] : [];
    
    // 直接保存responseCount和responseSize到resData中，确保响应式更新
    resData.responseCount = Array.isArray(data.responseCount) ? [...data.responseCount] : [];
    resData.responseSize = Array.isArray(data.responseSize) ? [...data.responseSize] : [];

    // 直接保存ipResponseTime到resData中，确保响应式更新
    resData.ipResponseTime = Array.isArray(data.ipResponseTime) ? [...data.ipResponseTime] : [];
    
    // 更新日志数据
   
      if (activeLogTab.value === 'access') {
        accessLogData.value = logsRes.data.data || [];
      } else {
        errorLogData.value = logsRes.data.data || [];
      }
      pagination.value.total = logsRes.data.total || 0;
      pagination.value.page = logsRes.data.page || pagination.value.page;
      pagination.value.size = logsRes.data.page_size || pagination.value.size;
    
    // 初始化图表
    nextTick(() => {
      initCharts();
      // 添加窗口resize事件监听
      window.addEventListener('resize', handleResize);
    });
    
    // 直接保存responseTimeByIp到resData中，确保响应式更新
    resData.responseTimeByIp = Array.isArray(data.responseTimeByIp) ? [...data.responseTimeByIp] : [];
  } catch (error) {
    ElMessage.error('加载节点数据失败');
  } finally {
    loading.value = false;
  }
}

// 添加统一的resize处理函数
function handleResize() {
  nextTick(() => {
    setTimeout(() => {
      if (statusCodeChart) statusCodeChart.resize();
      if (apiEndpointsChart) apiEndpointsChart.resize();
      if (nginxResponseTimeChart) nginxResponseTimeChart.resize();
      if (nginxResponseCountChart) nginxResponseCountChart.resize();
      if (apiResponseTimeChart) apiResponseTimeChart.resize();
      if (responseSizeChart) responseSizeChart.resize();
      if (businessHealthChart) businessHealthChart.resize();
      if (clientAnalysisChart) clientAnalysisChart.resize();
      // 调整G2Plot图表大小
      if (nginxResponseTimeG2Chart && nginxResponseTimeChartContainer.value) {
        // 使用G2Plot的正确方法更新图表大小
        nginxResponseTimeG2Chart.update({
          width: nginxResponseTimeChartContainer.value.clientWidth,
          height: nginxResponseTimeChartContainer.value.clientHeight
        });
      }
      if (ipResponseTimeChart) ipResponseTimeChart.resize();
    }, 0);
  });
}

// 添加统一的图表初始化函数
const initCharts = () => {
  
  // 销毁所有现有的图表实例
  if (statusCodeChart) {
    statusCodeChart.dispose();
    statusCodeChart = null;
  }
  if (apiEndpointsChart) {
    apiEndpointsChart.dispose();
    apiEndpointsChart = null;
  }
  if (nginxResponseTimeChart) {
    nginxResponseTimeChart.dispose();
    nginxResponseTimeChart = null;
  }
  if (apiResponseTimeChart) {
    apiResponseTimeChart.dispose();
    apiResponseTimeChart = null;
  }
  if (businessHealthChart) {
    businessHealthChart.dispose();
    businessHealthChart = null;
  }
  if (clientAnalysisChart) {
    clientAnalysisChart.dispose();
    clientAnalysisChart = null;
  }
  if (nginxResponseCountChart) {
    nginxResponseCountChart.dispose();
    nginxResponseCountChart = null;
  }
  if (responseSizeChart) {
    responseSizeChart.dispose();
    responseSizeChart = null;
  }
  if (ipResponseTimeChart) {
    ipResponseTimeChart.dispose();
    ipResponseTimeChart = null;
  }
  
  // 使用setTimeout确保DOM元素已经完全渲染
  setTimeout(() => {
    try {
      // 初始化状态码分布图表
      if (statusCodeChartContainer.value) {
        initStatusCodeChart();
      }
      
      // 初始化API端点分布图表
      if (document.getElementById('apiEndpointsChart')) {
        initApiEndpointsChart();
      }
      
      // 初始化Nginx响应时间图表
      if (nginxResponseTimeChartContainer.value) {
        initNginxResponseTimeChart();
      }
      
      // 初始化API响应时间图表
      if (apiResponseTimeChartContainer.value) {
        initApiResponseTimeChart();
      }
      
      // 初始化业务健康度图表
      if (businessHealthChartContainer.value) {
        initBusinessHealthChart();
      }
      
      // 初始化客户端分析图表
      if (clientAnalysisChartContainer.value) {
        initClientAnalysisChart();
      }
      
      // 初始化响应次数图表
      if (nginxResponseCountChartContainer.value) {
        initNginxResponseCountChart();
      }
      
      // 初始化响应大小图表
      if (responseSizeChartContainer.value) {
        initResponseSizeChart();
      }
      
      // 初始化IP响应时间图表
      if (ipResponseTimeChartContainer.value) {
        initIpResponseTimeChart();
      }
      
    } catch (error) {

    }
  }, 100);
};

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  // 销毁G2Plot图表实例
  if (nginxResponseTimeG2Chart) {
    nginxResponseTimeG2Chart.destroy();
    nginxResponseTimeG2Chart = null;
  }
  if (ipResponseTimeChart) {
    ipResponseTimeChart.dispose();
    ipResponseTimeChart = null;
  }
});

// 初始化状态码图表（优化UI）
function initStatusCodeChart() {
  if (!statusCodeChartContainer.value) return;

  // 先销毁已存在的图表实例
  if (statusCodeChart) {
    statusCodeChart.dispose();
    statusCodeChart = null;
  }

  statusCodeChart = echarts.init(statusCodeChartContainer.value);

  if (!statusCodeData.value || statusCodeData.value.length === 0) {
    statusCodeChart.setOption({
      title: {
        text: '暂无状态码数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 状态码颜色映射
  const getStatusCodeColor = (code: string) => {
    const codeNum = parseInt(code);
    if (codeNum >= 500) return '#F56C6C';  // 服务器错误
    if (codeNum >= 400) return '#E6A23C';  // 客户端错误
    if (codeNum >= 300) return '#409EFF';  // 重定向
    if (codeNum >= 200) return '#67C23A';  // 成功响应
    return '#909399';  // 其他
  };

  // 处理状态码数据，确保百分比值正确
  const statusData = statusCodeData.value.map(item => {
    // 确保percentage是数值，如果是0或不存在则计算百分比
    let percentage = item.percentage;
    if (percentage === 0 || percentage === undefined) {
      // 计算总请求数
      const totalRequests = statusCodeData.value.reduce((sum, code) => sum + code.count, 0);
      // 计算该状态码的百分比
      percentage = totalRequests > 0 ? item.count / totalRequests : 0;
    }
    
    return {
      name: item.code || '未知',
    value: item.count,
      percentage: (percentage * 100).toFixed(2), // 确保百分比正确格式化为2位小数
    itemStyle: {
        color: getStatusCodeColor(item.code || '0') // 防止code为undefined
    }
    };
  });
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `
          <div style="font-weight:bold;margin-bottom:5px;">状态码 ${params.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>请求数:</span>
            <span>${params.value.toLocaleString()}</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>占比:</span>
            <span>${parseFloat(params.data.percentage).toFixed(2)}%</span>
          </div>
        `;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      formatter: function(name) {
        return `${name}`;
      }
    },
    series: [
      {
        name: '状态码分布',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 10
        },
        emphasis: {
          focus: 'series',
          scaleSize: 10,
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: statusData
      }
    ]
  };
  
  statusCodeChart.setOption(option);
  
  // 添加点击事件 - 状态码下钻
  statusCodeChart.on('click', function(params) {
    const statusCode = params.name;
    if (statusCode) {
      // 使用统一的handleChartClick函数处理下钻
      handleChartClick('status', statusCode);
    }
  });
}

// 添加文本截断函数
function truncateText(text: string, maxLength: number = 20): string {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 修改API端点图表初始化函数，增加按数量从大到小排序
function initApiEndpointsChart() {
  const container = document.getElementById('apiEndpointsChart');
  if (!container) return;

  // 先销毁已存在的图表实例
  if (apiEndpointsChart) {
    apiEndpointsChart.dispose();
    apiEndpointsChart = null;
  }

  // 确保容器有尺寸
  if (container.offsetHeight === 0) {
    container.style.height = '300px';
  }

  apiEndpointsChart = echarts.init(container);

  // 检查是否有API端点数据
  if (!apiEndpointData.value || apiEndpointData.value.length === 0) {
    apiEndpointsChart.setOption({
      title: {
        text: '暂无API端点数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 对API端点数据按调用次数从大到小排序
  const sortedApiData = [...apiEndpointData.value].sort((a, b) => b.count - a.count);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#f1f1f1',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      padding: [10, 15],
      confine: false,
      appendToBody: true,
      formatter: function(params) {
        const data = params[0];
        const item = apiEndpointData.value.find(i => i.name === data.name);
        return `
          <div style="font-weight:bold;margin-bottom:5px;word-break:break-all;">${data.name}</div>
          <div style="display:flex;justify-content:space-between;margin:8px 0;align-items:center;border-bottom:1px dashed #eee;padding-bottom:5px;">
            <span>调用次数:</span>
            <span style="font-weight:600;">${data.value.toLocaleString()}</span>
          </div>
          ${item ? `
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>平均响应时间:</span>
            <span>${item.avgTime || '0ms'}</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>错误率:</span>
            <span>${item.errorRate || '0%'}</span>
          </div>
          ` : ''}
        `;
      }
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      axisLabel: {
        formatter: function(value) {
          return value.toLocaleString();
        }
      }
    },
    yAxis: {
      type: 'category',
      data: sortedApiData.map(item => item.name),
      axisLabel: {
        width: 150,
        overflow: 'truncate',
        interval: 0,
        formatter: function(value) {
          return truncateText(value, 25);
      }
      },
      inverse: true // 添加inverse:true使值大的在上面
    },
    series: [
      {
        type: 'bar',
        data: sortedApiData.map(item => ({
          value: item.count,
        itemStyle: {
            color: '#409EFF'
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          fontSize: 12
        }
      }
    ]
  };
  
  apiEndpointsChart.setOption(option);
  
  apiEndpointsChart.on('click', (params: any) => {
    const apiPath = params.name;
    if (apiPath) {
      handleChartClick('api', apiPath);
    }
  });
  
  // 添加窗口大小调整时重新渲染
  window.addEventListener('resize', () => {
    if (apiEndpointsChart) apiEndpointsChart.resize();
  });
}

// 修改Nginx响应时间图表初始化函数
function initNginxResponseTimeChart() {
  if (!nginxResponseTimeChartContainer.value) return;
  
  // 先销毁已存在的图表实例
  if (nginxResponseTimeChart) {
    nginxResponseTimeChart.dispose();
    nginxResponseTimeChart = null;
  }
  
  // 销毁G2Plot图表实例
  if (nginxResponseTimeG2Chart) {
    nginxResponseTimeG2Chart.destroy();
    nginxResponseTimeG2Chart = null;
  }
  
  // 检查是否有响应时间数据
  if (!resData.ipResponseTime || resData.ipResponseTime.length === 0) {
    // 创建一个echarts实例来显示无数据提示
    nginxResponseTimeChart = echarts.init(nginxResponseTimeChartContainer.value);
    nginxResponseTimeChart.setOption({
      title: {
        text: '暂无响应时间数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 处理直方图数据 - 转换为G2Plot所需格式
  const histogramData = resData.ipResponseTime.map(item => ({
    range: item.range,
    // 保存原始range值，用于筛选逻辑
    originalRange: item.range,
    value: item.value,
    // 根据响应时间区间设置不同颜色和分类
    category: (() => {
      // 解析区间范围，取中间值进行颜色判断
      const range = item.range;
      const isRange = range.includes('-');
      let avg = 0;
      
      if (isRange) {
        // 解析"0-20ms"格式
        const parts = range.replace('ms', '').split('-');
        const min = parseInt(parts[0]);
        const max = parseInt(parts[1]);
        avg = (min + max) / 2;
      } else if (range.startsWith('>')) {
        // 解析">1000ms"格式
        avg = parseInt(range.replace('ms', '').substring(1)) * 1.5;
      } else if (range.startsWith('<')) {
        // 解析"<500ms"格式
        avg = parseInt(range.replace('ms', '').substring(1)) * 0.5;
      } else {
        // 解析"500ms"格式
        avg = parseInt(range);
      }
      
      // 返回分类
      if (avg < 100) return '快速响应 (<100ms)';
      if (avg < 500) return '正常响应 (100-500ms)';
      if (avg < 1000) return '较慢响应 (500-1000ms)';
      return '慢速响应 (>1000ms)';
    })()
  })).map(item => {
    // 格式化显示的range值，将>=1000ms的值转换为秒
    const formattedRange = formatTimeRange(item.range);
    return {
      ...item,
      range: formattedRange
    };
  });
  
  // 添加格式化时间区间的函数
  function formatTimeRange(range) {
    // 如果不包含ms，直接返回
    if (!range.includes('ms')) return range;
    
    if (range.includes('-')) {
      // 处理"1000-2000ms"格式
      const parts = range.replace('ms', '').split('-');
      const min = parseInt(parts[0]);
      const max = parseInt(parts[1]);
      
      // 如果最小值大于等于1000，转换为秒
      if (min >= 1000) {
        return `${(min/1000).toFixed(1)}-${(max/1000).toFixed(1)}s`;
      }
      // 如果最小值小于1000但最大值大于等于1000
      else if (max >= 1000) {
        return `${min}ms-${(max/1000).toFixed(1)}s`;
      }
      return `${min}-${max}ms`;
    } 
    else if (range.startsWith('>')) {
      // 处理">5000ms"格式
      const value = parseInt(range.replace('ms', '').substring(1));
      if (value >= 1000) {
        return `>${(value/1000).toFixed(1)}s`;
      }
      return `>${value}ms`;
    } 
    else if (range.startsWith('<')) {
      // 处理"<500ms"格式
      const value = parseInt(range.replace('ms', '').substring(1));
      if (value >= 1000) {
        return `<${(value/1000).toFixed(1)}s`;
      }
      return `<${value}ms`;
    }
    // 处理纯数字格式
    const value = parseInt(range);
    if (value >= 1000) {
      return `${(value/1000).toFixed(1)}s`;
    }
    return `${value}ms`;
  }
  
  // 使用G2Plot创建Column图表
  nginxResponseTimeG2Chart = new Column(nginxResponseTimeChartContainer.value, {
    data: histogramData,
    xField: 'range',
    yField: 'value',
    seriesField: 'category',
    // 设置颜色映射
    color: ({ category }) => {
      switch (category) {
        case '快速响应 (<100ms)':
          return '#67C23A';
        case '正常响应 (100-500ms)':
          return '#409EFF';
        case '较慢响应 (500-1000ms)':
          return '#E6A23C';
        case '慢速响应 (>1000ms)':
          return '#F56C6C';
        default:
          return '#909399';
      }
    },
    // 美化图表
    columnStyle: {
      radius: [6, 6, 0, 0], // 柱状图圆角
    },
    // 图例配置
    legend: {
      position: 'top-right',
      itemName: {
        style: {
          fill: '#666',
          fontSize: 12,
          fontWeight: 500
        }
      },
      marker: {
        symbol: 'square',
        style: {
          r: 4,
          lineWidth: 0
        }
      }
    },
    // 改进元数据配置
    meta: {
      range: {
        alias: '响应时间区间',
        formatter: (val) => val
      },
      value: {
        alias: '请求数量',
        formatter: (val) => `${val.toLocaleString()} 次`
      }
    },
    // 改进X轴配置
    xAxis: {
      label: {
        autoRotate: true,
        autoHide: false,
        autoEllipsis: false,
        style: {
          fill: '#666',
          fontSize: 12
        }
      },
    
      line: {
        style: {
          stroke: '#E5E7EB'
        }
      },
      tickLine: {
        style: {
          stroke: '#E5E7EB'
        }
      }
    },
    // 改进Y轴配置
    yAxis: {
      label: {
        style: {
          fill: '#666',
          fontSize: 12
        },
        formatter: (val) => `${val.toLocaleString()}`
      },
      title: {
        text: '请求数量',
        style: {
          fontSize: 14,
          fill: '#666'
        }
      },
      grid: {
        line: {
          style: {
            stroke: '#E5E7EB',
            lineDash: [4, 4]
          }
        }
      }
    },
    // 改进提示框
    tooltip: {
      domStyles: {
        'g2-tooltip': {
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          borderRadius: '8px',
          padding: '12px 16px',
          color: '#333'
        }
      },
      formatter: (datum) => {
        // 使用原始区间进行显示，但格式化为易读形式
        return {
          name: '请求数量',
          value: `${Math.round(datum.value)} 次`,
          title: `响应时间: ${datum.range}`
        };
      }
    },

    // 配置刷选功能，增加重置按钮
    brush: {
      enabled: true,
      type: 'x-rect',
      action: 'filter',
      mask: {
        style: {
          fill: 'rgba(59, 130, 246, 0.2)',
          stroke: '#3B82F6',
          lineWidth: 1,
          fillOpacity: 0.4
        }
      },
      button: {
        text: '重置筛选',
        padding: [6, 10],
        textStyle: {
          default: {
            fill: '#333',
            fontSize: 12,
            fontWeight: 'normal'
          }
        },
        buttonStyle: {
          default: {
            fill: '#ffffff',
            stroke: '#d9d9d9',
            lineWidth: 1,
            radius: 4
          },
          active: {
            fill: '#f7f7f7',
            stroke: '#d9d9d9',
            lineWidth: 1,
            radius: 4
          }
        }
      }
    },
    // 标签配置 - 修改为显示在柱子上方
    label: {
      position: 'top',
      offsetY: -4,
      style: {
        fill: '#666',
        fontSize: 12,
        fontWeight: 500,
        shadowColor: 'rgba(255,255,255,0.85)',
        shadowBlur: 4
      },
      formatter: (data) => {
        return `${Math.round(data.value)}次`;
      }
    },
    // 动画配置
    animation: {
      appear: {
        animation: 'fade-in',
        duration: 800
      },
      update: {
        duration: 400
      }
    },
    // 图表边距 - 增加顶部边距以容纳标签
    padding: [50, 20, 50, 60],
    // 调整柱子间距
    marginRatio: 0.1
  });
  
  // 渲染图表
  nginxResponseTimeG2Chart.render();
  nginxResponseTimeG2Chart.on(G2.BRUSH_FILTER_EVENTS.AFTER_FILTER, () => {
    // 获取筛选后的数据
    const filteredData = nginxResponseTimeG2Chart?.chart.getData();
    
    // 如果数据为空或长度为0，不处理
    if (!filteredData || filteredData.length === 0) {
      return;
    }

    // 设置标记，表示这次更新来自图表筛选
    isFromBrush.value = true;

    try {
      // 获取第一个和最后一个区间
      const firstRange = filteredData[0].originalRange;
      const lastRange = filteredData[filteredData.length - 1].originalRange;

      // 解析区间值
      const getTimeValue = (range: string): number => {
        // 移除"ms"后缀
        const cleanRange = range.replace('ms', '');
        
        if (cleanRange.includes('-')) {
          // 处理"20-50"格式
          const [start] = cleanRange.split('-');
          return parseInt(start);
        } else if (cleanRange.startsWith('>')) {
          // 处理">1000"格式
          return parseInt(cleanRange.substring(1));
        } else if (cleanRange.startsWith('<')) {
          // 处理"<500"格式
          return parseInt(cleanRange.substring(1));
        }
        // 处理纯数字格式
        return parseInt(cleanRange);
      };

      // 检查最后一个区间是否包含">"符号
      const isLastRangeGreaterThan = lastRange.startsWith('>');
      
      // 获取开始时间
      const startTime = getTimeValue(firstRange);
      
      // 如果最后一个区间是">"格式，则使用">"格式构建响应时间范围
      const responseTimeRange = isLastRangeGreaterThan ? 
        `>${startTime}` : 
        `${startTime}-${getTimeValue(lastRange)}`;

      // 更新筛选条件
      logFilters.responseTime = responseTimeRange;
      responseTimeInput.value = responseTimeRange;
      
      // 设置为自定义模式并更新范围值
      responseTimePreset.value = 'custom';
      responseTimeMin.value = startTime;
      // 如果是">"格式，则将最大值设置为一个较大的数
      responseTimeMax.value = isLastRangeGreaterThan ? startTime * 2 : getTimeValue(lastRange);
      
      // 重置分页到第一页
      pagination.value.page = 1;

      // 调用接口获取新数据
      initPageData();
    } finally {
      // 重置标记
      setTimeout(() => {
        isFromBrush.value = false;
      }, 300);
    }
  });
}

// 修改Nginx响应次数图表初始化函数
function initNginxResponseCountChart() {
  if (!nginxResponseCountChartContainer.value) return;
  
  // 先销毁已存在的图表实例
  if (nginxResponseCountChart) {
    nginxResponseCountChart.dispose();
    nginxResponseCountChart = null;
  }
  
  // 创建新的图表实例
  nginxResponseCountChart = echarts.init(nginxResponseCountChartContainer.value);

  const responseCountData = resData.responseCount || [];
  
  if (responseCountData && responseCountData.length > 0) {
    // 获取所有唯一的IP和时间点
    const ipList = [...new Set(responseCountData.map(item => item.ip))];
    const timePoints = [...new Set(responseCountData.map(item => formatChartTime(item.time)))].sort();
    
    // 定义渐变色系供面积图使用
    const colorPalette = [
      ['rgba(24, 144, 255, 0.4)', 'rgba(24, 144, 255, 0.1)'],
      ['rgba(54, 206, 158, 0.4)', 'rgba(54, 206, 158, 0.1)'],
      ['rgba(139, 92, 246, 0.4)', 'rgba(139, 92, 246, 0.1)'],
      ['rgba(245, 158, 11, 0.4)', 'rgba(245, 158, 11, 0.1)'],
      ['rgba(236, 72, 153, 0.4)', 'rgba(236, 72, 153, 0.1)']
    ];
    
    // 为每个IP创建数据系列
    const series = ipList.map((ip, index) => {
      const ipData = responseCountData.filter(item => item.ip === ip);
      return {
        name: ip,
        type: 'line',
        smooth: true,
        symbol: 'emptyCircle',
        symbolSize: 6,
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colorPalette[index % colorPalette.length][0] },
            { offset: 1, color: colorPalette[index % colorPalette.length][1] }
          ])
        },
        data: timePoints.map(time => {
          const point = ipData.find(item => formatChartTime(item.time) === time);
          return point ? point.value : 0;
        })
      };
    });
    
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#f1f1f1',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        },
        padding: [10, 15],
        confine: false,
        appendToBody: true,
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: '#6a7985',
            type: 'dashed'
          }
        },
        formatter: function(params) {
          const timePoint = params[0].axisValue;
          let result = `<div style="font-weight:bold;margin-bottom:5px;">${timePoint}</div>`;
          let total = 0;
          
          // 按值从大到小排序并过滤掉值为0的数据
          const sortedParams = [...params]
            .filter(param => param.value > 0)
            .sort((a, b) => b.value - a.value);
          
          sortedParams.forEach(param => {
            total += param.value;
            result += `
              <div style="display:flex;justify-content:space-between;margin:3px 0;align-items:center;">
                <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:${param.color};margin-right:5px;"></span>
                <span style="flex:1;text-align:left;white-space:nowrap;margin-right:15px;">${param.seriesName}:</span>
                <span style="font-weight:500;">${param.value.toLocaleString()} 次</span>
              </div>
            `;
          });
          
          if (sortedParams.length > 1) {
            result += `
              <div style="margin-top:3px;padding-top:3px;border-top:1px solid #eee">
                <div style="display:flex;justify-content:space-between;margin:3px 0">
                  <span>总计:</span>
                  <span>${total.toLocaleString()} 次</span>
                </div>
              </div>
            `;
          }
          
          return result;
        }
      },
      legend: {
        data: ipList,
        top: 10,
        textStyle: {
          fontSize: 12,
          color: '#666'
        },
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15,
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '60px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: timePoints,
        axisLabel: {
          rotate: 45,
          interval: 0,
          fontSize: 11,
          color: '#666'
        },
        axisLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '响应次数',
        nameTextStyle: {
          padding: [0, 0, 0, 40],
          color: '#666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        }
      },
      series: series
    };
    
    // 应用配置
    nginxResponseCountChart.setOption(option);
    
    // 添加点击事件处理程序
    nginxResponseCountChart.on('click', (params: any) => {
      const ip = params.seriesName;
      if (ip) {
        handleChartClick('ip', ip);
      }
    });
  } else {
    nginxResponseCountChart.setOption({
      title: {
        text: '暂无响应次数数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
  }
}

// 修改响应大小图表初始化函数
function initResponseSizeChart() {
  if (!responseSizeChartContainer.value) return;
  
  if (responseSizeChart) {
    responseSizeChart.dispose();
    responseSizeChart = null;
  }
  
  responseSizeChart = echarts.init(responseSizeChartContainer.value);
  
  const responseSizeData = resData.responseSize || [];
  
  if (responseSizeData && responseSizeData.length > 0) {
    const ipList = [...new Set(responseSizeData.map(item => item.ip))];
    const timePoints = [...new Set(responseSizeData.map(item => formatChartTime(item.time)))].sort();
    
    // 定义新的颜色方案
    const colorPalette = [
      ['#1890FF', '#69C0FF'],
      ['#36CE9E', '#7FEBC2'],
      ['#8B5CF6', '#C4B5FD'],
      ['#F59E0B', '#FCD34D'],
      ['#EC4899', '#F9A8D4']
    ];
    
    const series = ipList.map((ip, index) => {
      const ipData = responseSizeData.filter(item => item.ip === ip);
      return {
        name: ip,
        type: 'bar',
        barWidth: '60%',
        stack: 'total',
        data: timePoints.map(time => {
          const point = ipData.find(item => formatChartTime(item.time) === time);
          // 直接使用原始值，不进行转换
          return point ? Number(point.value) : 0;
        }),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colorPalette[index % colorPalette.length][0] },
            { offset: 1, color: colorPalette[index % colorPalette.length][1] }
          ]),
          borderRadius: [3, 3, 0, 0]
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        }
      };
    });
    
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#f1f1f1',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        },
        padding: [10, 15],
        confine: false,
        appendToBody: true,
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.03)'
          }
        },
        formatter: function(params) {
          const timePoint = params[0].axisValue;
          let result = `<div style="font-weight:bold;margin-bottom:5px;">${timePoint}</div>`;
          let total = 0;
          
          // 按值从大到小排序并过滤掉值为0的数据
          const sortedParams = [...params]
            .filter(param => param.value > 0)
            .sort((a, b) => b.value - a.value);
          
          sortedParams.forEach(param => {
            total += param.value;
            result += `
              <div style="display:flex;justify-content:space-between;margin:3px 0;align-items:center;">
                <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:${param.color};margin-right:5px;"></span>
                <span style="flex:1;text-align:left;white-space:nowrap;margin-right:15px;">${param.seriesName}:</span>
                <span style="font-weight:500;">${param.value.toLocaleString()} KB</span>
              </div>
            `;
          });
          
          if (sortedParams.length > 1) {
            result += `
              <div style="margin-top:3px;padding-top:3px;border-top:1px solid #eee">
                <div style="display:flex;justify-content:space-between;margin:3px 0">
                  <span>总计:</span>
                  <span>${total.toLocaleString()} KB</span>
                </div>
              </div>
            `;
          }
          
          return result;
        }
      },
      legend: {
        data: ipList,
        top: 10,
        textStyle: {
          fontSize: 12,
          color: '#666'
        },
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15,
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '60px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timePoints,
        axisLabel: {
          rotate: 45,
          interval: 0,
          fontSize: 11,
          color: '#666'
        },
        axisLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '响应大小(KB)',
        nameTextStyle: {
          padding: [0, 0, 0, 40],
          color: '#666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666',
          formatter: '{value} KB'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        }
      },
      series: series
    };
    
    responseSizeChart.setOption(option);
  } else {
    responseSizeChart.setOption({
      title: {
        text: '暂无响应大小数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
  }
}

// 生成时间标签函数
function generateTimeLabels(count: number): string[] {
  const result: string[] = [];
  const now = dayjs();
  
  for (let i = count - 1; i >= 0; i--) {
    result.push(now.subtract(i, 'hour').format('HH:mm'));
  }
  
  return result;
}

// 初始化业务健康度图表
function initBusinessHealthChart() {
  if (!businessHealthChartContainer.value) return;

  // 先销毁已存在的图表实例
  if (businessHealthChart) {
    businessHealthChart.dispose();
    businessHealthChart = null;
  }

  businessHealthChart = echarts.init(businessHealthChartContainer.value);

  // 检查是否有业务健康度数据
  if (!businessHealthData.value || businessHealthData.value.length === 0) {
    // 显示空图表
    businessHealthChart.setOption({
      title: {
        text: '暂无业务健康度数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 定义一组渐变色
  const colorPalette = [
    ['#1890FF', '#69C0FF'], // 蓝色系
    ['#36CE9E', '#7FEBC2'], // 绿色系
    ['#8B5CF6', '#C4B5FD'], // 紫色系
    ['#F59E0B', '#FCD34D'], // 橙色系
    ['#EC4899', '#F9A8D4'], // 粉色系
    ['#10B981', '#6EE7B7'], // 翠绿色系
    ['#3B82F6', '#93C5FD'], // 天蓝色系
    ['#9333EA', '#C084FC'], // 深紫色系
    ['#F97316', '#FDBA74'], // 深橙色系
    ['#14B8A6', '#5EEAD4']  // 青色系
  ];
  
  // 准备雷达图数据
  const indicatorData = [
    { name: '健康分值', max: 100 },
    { name: '调用量', max: Math.max(...businessHealthData.value.map(item => item.callVolume)) },
    { name: '响应时间', max: Math.max(...businessHealthData.value.map(item => item.avgResponseTime)) },
    { name: '错误率', max: Math.max(...businessHealthData.value.map(item => item.errorRate * 100)) * 1.2 }
  ];
  
  const seriesData = businessHealthData.value.map((item, index) => {
    return {
      value: [
        item.healthScore,
        item.callVolume,
        item.avgResponseTime,
        item.errorRate * 100
      ],
      name: item.moduleName,
      // 使用渐变色
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: colorPalette[index % colorPalette.length][0] },
          { offset: 1, color: colorPalette[index % colorPalette.length][1] }
        ])
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: colorPalette[index % colorPalette.length][0] },
          { offset: 1, color: colorPalette[index % colorPalette.length][1] }
        ]),
        opacity: 0.3
      }
    };
  });
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#f1f1f1',
      borderWidth: 1,
      padding: [10, 15],
      confine: false,
      appendToBody: true,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        const data = params.data;
        const moduleData = businessHealthData.value.find(item => item.moduleName === data.name);
        if (!moduleData) return '';
        
        return `
          <div style="font-weight:bold;margin-bottom:5px;">${data.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>健康分值:</span>
            <span>${moduleData.healthScore}</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>调用量:</span>
            <span>${moduleData.callVolume}</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>平均响应时间:</span>
            <span>${moduleData.avgResponseTime}ms</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>错误率:</span>
            <span>${(moduleData.errorRate * 100).toFixed(2)}%</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>状态:</span>
            <span>${moduleData.status === 'good' ? '良好' : 
                   moduleData.status === 'warning' ? '警告' : '危险'}</span>
          </div>
        `;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      width: '15%',
      formatter: function(name) {
        return truncateText(name, 15);
      },
      textStyle: {
        fontSize: 12,
        color: '#666'
      }
    },
    radar: {
      indicator: indicatorData,
      shape: 'circle',
      center: ['40%', '50%'],
      radius: '60%',
      splitNumber: 5,
      axisName: {
        color: '#333',
        fontSize: 12
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(255, 255, 255, 0.8)'],
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: 10
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    series: [
      {
        name: '业务健康度',
        type: 'radar',
        emphasis: {
          lineStyle: {
            width: 4
          }
        },
        data: seriesData
      }
    ]
  };
  
  businessHealthChart.setOption(option);
  
  // 添加点击事件 - 筛选日志
  businessHealthChart.on('click', function(params) {
    // 防止重复调用
    if (isFilterChanging.value) return;
    isFilterChanging.value = true;
    
    try {
      // 尝试获取模块名称
      let moduleName = "";
      
      if (params.seriesName) {
        // 尝试从series名称获取
        moduleName = params.seriesName;
      } else if (params.name) {
        // 尝试从项目名称获取
        moduleName = params.name as string;
      } else if (params.data) {
        // 尝试从数据对象获取
        const dataItem = params.data as any;
        if (dataItem.moduleName) {
          moduleName = dataItem.moduleName;
        }
      }
      
      // 如果无法获取模块名称，则返回
      if (!moduleName) {
  
        isFilterChanging.value = false;
        return;
      }
      
      // 设置API端点输入框的值
      apiEndpointInput.value = moduleName;
      
      // 清除其他筛选条件
      statusCodeInput.value = '';
      ipSearchInput.value = '';
      
      // 直接应用筛选条件
      logFilters.apiEndpoint = moduleName;
      logFilters.statusCode = '';
      logFilters.sourceIp = ''; // 修改为sourceIp
      pagination.value.page = 1;
      
      // 重新获取所有数据
      initPageData();
      
      // 显示消息提示用户已应用筛选
      ElMessage.success(`已筛选 ${moduleName} 模块的数据`);
    } catch (error) {

      ElMessage.error('处理图表点击失败');
    }
    
    // 重置防重复标志
    setTimeout(() => {
      isFilterChanging.value = false;
    }, 300);
  });
  
  // 添加窗口大小调整时重新渲染
  window.addEventListener('resize', () => {
    if (businessHealthChart) businessHealthChart.resize();
  });
}

// 新增客户端分析维度切换
const clientAnalysisDimension = ref('ip'); // 默认显示IP维度

// 客户端分析数据处理函数
function processClientAnalysisData(dimension: string) {
  const data = clientAnalysisData.value;
  if (!data || data.length === 0) return [];

  // 根据维度分组数据
  const groupedData = data.reduce((acc, item) => {
    let key = '';
    switch (dimension) {
      case 'ip':
        key = item.clientType;
        break;
      case 'os':
        key = item.os || '未知';
        break;
      case 'browser':
        key = item.browser || '未知';
        break;
      case 'browserVersion':
        key = `${item.browser || '未知'} ${item.browserVersion || ''}`.trim();
        break;
    }

    if (!acc[key]) {
      acc[key] = {
        name: key,
        count: 0
      };
    }
    acc[key].count += item.count;
    return acc;
  }, {} as Record<string, { name: string; count: number }>);

  // 计算总数和百分比
  const total = Object.values(groupedData).reduce((sum, item) => sum + item.count, 0);
  
  return Object.values(groupedData).map(item => ({
    name: item.name,
    value: item.count,
    percentage: ((item.count / total) * 100).toFixed(1)
  }));
}

// 修改初始化客户端分析图表函数
function initClientAnalysisChart() {
  if (!clientAnalysisChartContainer.value) return;

  // 先销毁已存在的图表实例
  if (clientAnalysisChart) {
    clientAnalysisChart.dispose();
    clientAnalysisChart = null;
  }

  clientAnalysisChart = echarts.init(clientAnalysisChartContainer.value);

  // 检查是否有客户端分析数据
  if (!clientAnalysisData.value || clientAnalysisData.value.length === 0) {
    // 显示空图表
    clientAnalysisChart.setOption({
      title: {
        text: '暂无客户端分析数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 处理数据
  const processedData = processClientAnalysisData(clientAnalysisDimension.value);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const data = params.data;
        return `
          <div style="font-weight:bold;margin-bottom:5px;">${data.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>请求数量:</span>
            <span>${data.value.toLocaleString()}</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0">
            <span>占比:</span>
            <span>${data.percentage}%</span>
          </div>
        `;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      formatter: function(name) {
        return truncateText(name, 20);
      },
      data: processedData.map(item => item.name)
    },
    series: [
      {
        name: '客户端分析',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            return truncateText(params.name, 15) + '\n' + params.percent + '%';
          },
          fontSize: 12
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: processedData
      }
    ]
  };
  
  clientAnalysisChart.setOption(option);
  
  // 只保留窗口大小调整事件
  window.addEventListener('resize', () => {
    if (clientAnalysisChart) clientAnalysisChart.resize();
  });
}

// 监听维度变化
watch(clientAnalysisDimension, () => {
  if (clientAnalysisChart) {
    initClientAnalysisChart();
  }
});

// 设置页面加载事件
onMounted(() => {
  window.addEventListener('resize', handleResize);
  initPageData();
});

// 在组件更新时重新初始化图表 - 确保数据变化时图表能正确更新
watch([
  () => statusCodeData.value,
  () => apiEndpointData.value,
  () => sourceIpData.ips,
  () => apiResponseTimeSeries.value,
  () => businessHealthData.value,
  () => clientAnalysisData.value,
  () => resData.responseCount,
  () => resData.responseSize,
  () => resData.ipResponseTime,
  () => resData.responseTimeByIp
], () => {
  // 使用nextTick确保DOM更新后再初始化图表
  nextTick(() => {
    initCharts();
  });
}, { deep: true, immediate: false });

// 修改时间监听函数
watch(() => dateTimeStore.$state, async (newValue) => {
  try {
    loading.value = true;
    // 获取UTC时间戳
    const startTime = convertTimestampToSeconds(newValue.begin);
    const endTime = convertTimestampToSeconds(newValue.over);
    
    const analysisParams = {
      ip: nodeData.ip,
      type: route.query.type,
      start_time: startTime,
      end_time: endTime,
      status: logFilters.statusCode,
      api_path: logFilters.apiEndpoint,
      source_ip: logFilters.sourceIp,
      response_time: logFilters.responseTime, // 使用响应时间而不是查询
      ports: nodeData.ports // 添加ports参数
    };

    const logParams = {
      start_time: startTime,
      end_time: endTime,
      ip: nodeData.ip,
      source_ip: logFilters.sourceIp,
      log_type: activeLogTab.value,
      response_time: logFilters.responseTime, // 使用响应时间而不是查询
      status: logFilters.statusCode,
      api_path: logFilters.apiEndpoint,
      page: pagination.value.page,
      page_size: pagination.value.size,
      type: route.query.type,
      ports: nodeData.ports // 添加ports参数
    };

    // 并行请求数据
    const [analysisRes, logsRes] = await Promise.all([
        getNginxAnalysis(analysisParams),getNginxLogs(logParams)
      // api.post('/nginx/analysis', JSON.stringify(analysisParams), { baseURL: '/mock/' }),
      // api.post('/nginx/log', JSON.stringify(logParams), { baseURL: '/mock/' })
    ]);

    // 处理分析数据
    const data = analysisRes.data;

    // 重要：确保在更新数据之前先销毁现有图表实例
    if (nginxResponseTimeChart) {
      nginxResponseTimeChart.dispose();
      nginxResponseTimeChart = null;
    }

    // 销毁G2Plot图表实例
    if (nginxResponseTimeG2Chart) {
      nginxResponseTimeG2Chart.destroy();
      nginxResponseTimeG2Chart = null;
    }

    // 更新所有数据，确保响应式更新
    statusCodeData.value = Array.isArray(data.statusCode) ? [...data.statusCode] : [];
    apiEndpointData.value = Array.isArray(data.api) ? [...data.api] : [];
    businessHealthData.value = Array.isArray(data.businessHealth) ? [...data.businessHealth] : [];
    clientAnalysisData.value = Array.isArray(data.clientAnalysis) ? [...data.clientAnalysis] : [];
    resData.responseCount = Array.isArray(data.responseCount) ? [...data.responseCount] : [];
    resData.responseSize = Array.isArray(data.responseSize) ? [...data.responseSize] : [];
    resData.ipResponseTime = Array.isArray(data.ipResponseTime) ? [...data.ipResponseTime] : [];
    resData.responseTimeByIp = Array.isArray(data.responseTimeByIp) ? [...data.responseTimeByIp] : [];
    
    // 更新数据
    if (data.responseTime) {
      nginxResponseTimeData.timestamps = data.responseTime.map(item => item.name);
      nginxResponseTimeData.counts = data.responseTime.map(item => item.value);
    }

    // 更新API调用时间数据
    apiCallTimeData.timestamps = nginxResponseTimeData.timestamps;
    apiCallTimeData.callCounts = nginxResponseTimeData.counts.map(val => Math.floor(val * 1.5));
    apiCallTimeData.responseTimes = nginxResponseTimeData.counts.map(val => Math.floor(val * 0.8));

    // 使用nextTick确保DOM更新后再初始化图表
    nextTick(() => {
      // 重新初始化所有图表
      initCharts();
    });

    // 处理日志数据
    if (activeLogTab.value === 'access') {
      accessLogData.value = logsRes.data.data || [];
    } else {
      errorLogData.value = logsRes.data.data || [];
    }
    pagination.value.total = logsRes.data.total || 0;
    pagination.value.page = logsRes.data.page || pagination.value.page;
    pagination.value.size = logsRes.data.page_size || pagination.value.size;

  } catch (error) {
    ElMessage.error('更新数据失败');
  } finally {
    loading.value = false;
  }
}, { deep: true });




// 在 script setup 中添加
const handleChartClick = (filterType: 'status' | 'api' | 'ip', value: string) => {
  if (isFilterChanging.value) return;
  isFilterChanging.value = true;
  
  try {
    const currentFilters = { ...logFilters };
    
    switch (filterType) {
      case 'status':
        currentFilters.statusCode = value;
        statusCodeInput.value = value;
        break;
      case 'api':
        currentFilters.apiEndpoint = value;
        apiEndpointInput.value = value;
        break;
      case 'ip':
        currentFilters.sourceIp = value; // 修改为sourceIp
        ipSearchInput.value = value;
        break;
    }
    
    Object.assign(logFilters, currentFilters);
    
    pagination.value.page = 1;
    initPageData();
    
    ElMessage.success(`已添加${filterType === 'status' ? '状态码' : filterType === 'api' ? 'API端点' : '源IP'}筛选: ${value}`);
  } catch (error) {

    ElMessage.error('筛选失败');
  } finally {
    setTimeout(() => {
      isFilterChanging.value = false;
    }, 300);
  }
};

// 初始化API响应时间图表
function initApiResponseTimeChart() {
  if (!apiResponseTimeChartContainer.value) return;

  if (apiResponseTimeChart) {
    apiResponseTimeChart.dispose();
    apiResponseTimeChart = null;
  }
  apiResponseTimeChart = echarts.init(apiResponseTimeChartContainer.value);

  // 检查是否有API响应时间数据
  if (!apiResponseTimeSeries.value || apiResponseTimeSeries.value.length === 0) {
    apiResponseTimeChart.setOption({
      title: {
        text: '暂无API响应时间数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }

  // 定义渐变色系
    const colorPalette = [
      ['#1890FF', '#36CBCB'],
      ['#36CE9E', '#66E6A6'],
      ['#975FE4', '#7B9DF1'],
      ['#F59E0B', '#FFCE56'],
      ['#EC4899', '#F87171']
    ];

  const series: EchartsSeriesItem[] = [];

  // 获取所有唯一的时间点
  let allTimePoints: string[] = [];
  apiResponseTimeSeries.value.forEach(apiItem => {
    const timePoints = apiItem.times.map(item => formatChartTime(item.time));
    allTimePoints = [...allTimePoints, ...timePoints];
  });
  // 去重并排序时间点
  allTimePoints = [...new Set(allTimePoints)].sort();
  
  // 为每个API创建一个系列
    apiResponseTimeSeries.value.forEach((apiItem, index) => {
      // 查找原始数据以获取avgTime和calls信息
      const originalData = resData.avargeResponse.find(item => item.service === apiItem.api);
      const avgTime = originalData ? originalData.avgTime : '';
      const calls = originalData ? originalData.calls : 0;
    
    // 计算所有时间点的实际平均值（而不是使用服务器返回的平均值）
    const allValues = apiItem.times.map(item => item.value);
    const calculatedAvg = allValues.length > 0 
      ? allValues.reduce((sum, val) => sum + val, 0) / allValues.length 
      : 0;
    const avgTimeValue = calculatedAvg;
      
      // 在名称中显示平均响应时间和调用次数
    const formattedName = `${apiItem.api} (平均: ${avgTimeValue.toFixed(2)}ms, 调用: ${calls})`;
      
      series.push({
        name: formattedName,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        data: apiItem.times.map(item => item.value),
        animationDuration: 1500,
        lineStyle: {
          width: 3
        },
        itemStyle: {
          color: colorPalette[index % colorPalette.length][0]
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 2,
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      });
    });
  
    const option = {
      tooltip: {
        trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#f1f1f1',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        },
        padding: [10, 15],
      confine: false,
      appendToBody: true,
        axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#6a7985',
          type: 'dashed'
          }
        },
        formatter: function(params) {
          const timePoint = params[0].axisValue;
          let result = `<div style="font-weight:bold;margin-bottom:5px;">${timePoint}</div>`;
          
        // 按值从大到小排序，以便在tooltip中更好地比较
        const sortedParams = [...params].sort((a, b) => b.value - a.value);
        
        sortedParams.forEach(param => {
            const apiName = param.seriesName.split(' (')[0];
          // 从系列名称中提取平均值信息 (平均: Xms, 调用: Y)
          const avgValueMatch = param.seriesName.match(/平均: ([\d.]+)ms/);
          const avgValue = avgValueMatch ? parseFloat(avgValueMatch[1]) : 0;
              const currentValue = param.value;
          
          // 计算与平均值的差异百分比
              let diffText = '';
          let diffPercentValue = 0;
              
              if (avgValue > 0) {
            diffPercentValue = ((currentValue - avgValue) / avgValue * 100);
            const diffPercent = diffPercentValue.toFixed(1);
            const diffColor = diffPercentValue > 0 ? '#F56C6C' : '#67C23A';
            const diffIcon = diffPercentValue > 0 ? '↑' : '↓';
              // 更醒目的差异显示
              diffText = `<span style="color:${diffColor};font-weight:bold;">${diffIcon} ${Math.abs(parseFloat(diffPercent))}%</span>`;
              }
          
          // 找出该API的所有信息
          const originalData = resData.avargeResponse.find(item => item.service === apiName);
          const calls = originalData ? originalData.calls : 0;
              
              result += `
              <div style="display:flex;justify-content:space-between;margin:8px 0;align-items:center;border-bottom:1px dashed #eee;padding-bottom:5px;">
                <div style="display:flex;align-items:center;">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${param.color};margin-right:8px;"></span>
                  <span style="white-space:nowrap;max-width:200px;overflow:hidden;text-overflow:ellipsis;">${apiName}</span>
                </div>
                <div style="display:flex;align-items:center;margin-left:15px;">
                  <span style="font-weight:600;">${Math.round(currentValue * 1000)}ms</span>
                  <span style="margin-left:8px;">${diffText}</span>
                </div>
              </div>
              <div style="display:flex;justify-content:space-between;font-size:11px;color:#666;margin-left:18px;margin-bottom:8px;">
                <span>平均: <b>${Math.round(avgValue * 1000)}ms</b></span>
            <span>调用: ${calls}次</span>
                </div>
              `;
          });
          
          return result;
        }
      },
      legend: {
        type: 'scroll',
        data: series.map(s => s.name),
        top: 30,
        textStyle: {
          fontSize: 12,
          color: '#666'
        },
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15,
        formatter: function(name) {
          return truncateText(name.split(' (')[0], 30);
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '80px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
      data: allTimePoints.length > 0 ? allTimePoints : generateTimeLabels(24),
        axisLabel: {
        interval: 'auto',
          rotate: 45,
          fontSize: 11,
          color: '#666'
        },
        axisLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '响应时间(ms)',
        nameTextStyle: {
          padding: [0, 0, 0, 40],
          color: '#666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666',
          formatter: function(value) {
            return Math.round(value * 1000) + ' ms';
          }
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        }
      },
      series: series
    };
  
    apiResponseTimeChart.setOption(option);
  
  // 添加窗口大小调整时重新渲染
  window.addEventListener('resize', () => {
    if (apiResponseTimeChart) apiResponseTimeChart.resize();
  });
}

// 在script部分添加新的变量和函数
const ipResponseTimeChartContainer = ref<HTMLElement | null>(null);
let ipResponseTimeChart: echarts.ECharts | null = null;

// 添加新的图表初始化函数
function initIpResponseTimeChart() {
  if (!ipResponseTimeChartContainer.value) return;

  if (ipResponseTimeChart) {
    ipResponseTimeChart.dispose();
    ipResponseTimeChart = null;
  }

  ipResponseTimeChart = echarts.init(ipResponseTimeChartContainer.value);

  // 直接使用resData.responseTimeByIp作为数据源
  const responseTimeData = resData.responseTimeByIp || [];

  if (!responseTimeData || responseTimeData.length === 0) {
    ipResponseTimeChart.setOption({
      title: {
        text: '暂无IP响应时间数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      }
    });
    return;
  }

  // 从数据中提取所有唯一的IP和时间点
  const ipList = [...new Set(responseTimeData.map(item => item.ip))];
  const timePoints = [...new Set(responseTimeData.map(item => item.time))].sort();
  
  // 定义渐变色系供曲线图使用
  const colorPalette = [
    ['#1890FF', '#69C0FF'],
    ['#36CE9E', '#7FEBC2'],
    ['#8B5CF6', '#C4B5FD'],
    ['#F59E0B', '#FCD34D'],
    ['#EC4899', '#F9A8D4']
  ];
  
  const series = ipList.map((ip, index) => {
    const ipData = responseTimeData.filter(item => item.ip === ip);
    return {
      name: ip,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      data: timePoints.map(time => {
        const point = ipData.find(item => item.time === time);
        return point ? point.value * 1000 : null; // 转换为毫秒
      }),
      lineStyle: {
        width: 3
      },
      itemStyle: {
        color: colorPalette[index % colorPalette.length][0]
      },
      areaStyle: {
        opacity: 0.15,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: colorPalette[index % colorPalette.length][0] },
          { offset: 1, color: colorPalette[index % colorPalette.length][1] }
        ])
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          borderWidth: 3,
          borderColor: '#fff',
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    };
  });
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#f1f1f1',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((item: any) => {
          result += `${item.marker}${item.seriesName}: ${item.value ? item.value : '-'} ms<br/>`;
        });
        return result;
      },
      padding: [10, 15],
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#6a7985',
          type: 'dashed'
        }
      }
    },
    legend: {
      data: ipList,
      top: 10,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '60px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timePoints.map(time => {
        // 直接从UTC时间字符串中提取小时数
        const hour = time.slice(11, 13);
        return `${hour}:00`;
      }),
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#eee'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '响应时间(ms)',
      nameTextStyle: {
        padding: [0, 0, 0, 40],
        color: '#666'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: '{value} ms'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      }
    },
    series: series
  };
  
  ipResponseTimeChart.setOption(option);
  
  ipResponseTimeChart.on('click', (params) => {
    const ip = params.seriesName;
    if (ip) {
      handleChartClick('ip', ip);
    }
  });
}
</script>

<style scoped>
.nginx-analysis-container {
  padding: 24px;
  background-color: #f8fafc;
  background-image: linear-gradient(to bottom, #f1f5f9, #f8fafc);
  min-height: calc(100vh - 60px);
}

/* 顶部导航栏样式优化 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 20, 80, 0.06);
  margin-bottom: 24px;
  border: 1px solid rgba(226, 232, 240, 0.7);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.top-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  z-index: 1;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  padding: 10px 18px;
  transition: all 0.3s ease;
  border-radius: 10px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  &:hover {
    transform: translateX(-3px);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  }
}

.node-basic-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 12px;
  position: relative;
}

.node-basic-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
  border-radius: 4px;
}

.node-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1f36;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-tag {
  padding: 5px 14px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.3px;
}

.node-details {
  display: flex;
  align-items: center;
  gap: 24px;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 16px;
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.7);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #0f172a;
  font-weight: 600;
  background: rgba(224, 242, 254, 0.5);
  padding: 2px 8px;
  border-radius: 6px;
  border: 1px solid rgba(186, 230, 253, 0.3);
}

/* 筛选区域样式优化 */
.filter-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 24px;
  box-shadow: 0 6px 24px rgba(0, 20, 80, 0.06);
  border: 1px solid rgba(226, 232, 240, 0.7);
  position: relative;
}

.filter-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: nowrap;
}

.response-time-filter {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
}

.filter-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #1e293b;
}

.filter-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.custom-range {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.range-separator {
  margin: 0 8px;
  color: #64748b;
}

.range-unit {
  margin-left: 8px;
  color: #64748b;
}

.custom-btn {
  margin-left: 10px;
}

.status-input,
.api-input,
.ip-input {
  min-width: 180px;
  flex: 1;
  
  :deep(.el-input__wrapper) {
    box-shadow: none;
    border-radius: 10px;
    padding: 10px 14px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #94a3b8;
      background-color: #f1f5f9;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      background-color: #ffffff;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
    }
  }
  
  :deep(.el-input__icon) {
    color: #64748b;
  }
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: 16px;
  white-space: nowrap;
}

.filter-actions :deep(.el-button) {
  border-radius: 10px;
  padding: 10px 18px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 图表卡片样式优化 */
.dashboard-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  height: 420px;
  border-radius: 16px;
  background: #ffffff;
  border: 1px solid rgba(226, 232, 240, 0.7);
  box-shadow: 0 6px 24px rgba(0, 20, 80, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &:hover {
    box-shadow: 0 12px 30px rgba(0, 20, 80, 0.1);
    transform: translateY(-2px);
  }
}

.card-header {
  background: linear-gradient(to right, #ffffff, #f8fafc);
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.card-header span {
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 14px;
    width: 3px;
    background: linear-gradient(to bottom, #3b82f6, #60a5fa);
    border-radius: 3px;
  }
}

.chart-container {
  flex: 1;
  height: calc(100% - 44px) !important;
  min-height: 300px;
  padding: 16px;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  width: 100%;
}

.log-tabs,
.client-analysis-tabs {
  margin-left: auto;
  
  :deep(.el-radio-button__inner) {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #e2e8f0;
    height: auto;
    font-weight: 500;
    transition: all 0.2s ease;
  }
  
  :deep(.el-radio-button:first-child .el-radio-button__inner) {
    border-left: 1px solid #e2e8f0;
    border-radius: 8px;
  }
  
  :deep(.el-radio-button:last-child .el-radio-button__inner) {
    border-radius: 8px;
  }
  
  :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
    background-color: #3b82f6;
    border-color: #3b82f6;
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }
}

/* 客户端分析标签组样式 */
.client-analysis-tabs {
  margin-left: 32px !important;
}

.log-card {
  border-radius: 16px;
  background: #ffffff;
  border: 1px solid rgba(226, 232, 240, 0.7);
  box-shadow: 0 6px 24px rgba(0, 20, 80, 0.06);
  margin-bottom: 24px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px 0;
  display: flex;
  justify-content: center;
  
  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: #3b82f6;
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
  }
  
  :deep(.el-pagination.is-background .el-pager li) {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    margin: 0 3px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.warning-text {
  color: #f59e0b;
  font-weight: 600;
}

:deep(.el-table) {
  border-radius: 12px;
  overflow: hidden;
  
  th {
    background-color: #f1f5f9 !important;
    font-weight: 600;
    color: #1e293b;
    padding: 14px 16px;
    border-bottom: 2px solid #e2e8f0;
  }
  
  td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
  }
  
  .el-table__row {
    transition: all 0.2s ease;
    
    &:hover > td {
      background-color: #f1f5f9;
    }
    
    &:nth-child(even) {
      background-color: #f8fafc;
    }
  }
  
  .el-tag {
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
    
    &.el-tag--info {
      background-color: #f1f5f9;
      border-color: #cbd5e1;
      color: #475569;
    }
    
    &.el-tag--success {
      background-color: #ecfdf5;
      border-color: #a7f3d0;
      color: #10b981;
    }
    
    &.el-tag--warning {
      background-color: #fffbeb;
      border-color: #fde68a;
      color: #f59e0b;
    }
    
    &.el-tag--danger {
      background-color: #fef2f2;
      border-color: #fecaca;
      color: #ef4444;
    }
  }
}

@media (max-width: 1400px) {
  .filter-content {
    flex-wrap: wrap;
  }
  
  .filter-actions {
    margin-left: 0;
    flex: 0 0 100%;
    justify-content: flex-end;
    margin-top: 16px;
  }
  
  .top-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .node-details {
    align-self: stretch;
  }
}

@media (max-width: 1200px) {
  .dashboard-row {
    grid-template-columns: 1fr;
  }
  
  .chart-card {
    height: 380px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .node-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .nginx-analysis-container {
    padding: 16px;
  }
  
  .chart-card {
    height: 340px;
  }
  
  .filter-content > * {
    flex: 0 0 100%;
  }
  
  .log-tabs,
  .client-analysis-tabs {
    :deep(.el-radio-button__inner) {
      padding: 6px 10px;
      font-size: 12px;
    }
  }
}
</style> 
