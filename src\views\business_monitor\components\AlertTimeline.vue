<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import * as dayjsLib from 'dayjs';
const dayjs = dayjsLib.default || dayjsLib;

// 定义props
const props = defineProps({
  alerts: {
    type: Array,
    default: () => []
  }
});

const loading = ref(false);
const searchKeyword = ref('');
const statusFilter = ref('');
const priorityFilter = ref('');

// 告警历史记录对话框
const historyDialog = ref(false);
const currentAlertHistory = ref(null);

// 主页面只显示最近5条告警
const visibleAlerts = computed(() => {
  return props.alerts.slice(0, 5);
});

// 抽屉中显示过滤后的告警
const filteredAlerts = computed(() => {
  let result = [...props.alerts];
  
  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(alert => 
      alert.hostname?.toLowerCase().includes(keyword) || 
      alert.description?.toLowerCase().includes(keyword) ||
      alert.hostip?.toLowerCase().includes(keyword)
    );
  }
  
  // 按状态过滤
  if (statusFilter.value) {
    result = result.filter(alert => alert.status === statusFilter.value);
  }
  
  // 按优先级过滤
  if (priorityFilter.value) {
    result = result.filter(alert => alert.priority === priorityFilter.value);
  }
  
  // 按时间排序（最新的在前面）
  return result.sort((a, b) => new Date(b.lastchange) - new Date(a.lastchange));
});

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

// 获取告警类型对应的标签类型和颜色
function getAlertType(priority) {
  switch (priority) {
    case '灾难':
      return { type: 'danger', color: '#E45959' };
    case '严重':
      return { type: 'danger', color: '#E97659' };
    case '一般严重':
      return { type: 'warning', color: '#FFA059' };
    case '警告':
      return { type: 'warning', color: '#FFC859' };
    case '信息':
      return { type: 'info', color: '#7499FF' };
    default:
      return { type: 'info', color: '#909399' };
  }
}

// 显示单条告警的历史记录
const showAlertHistory = (alert) => {
  currentAlertHistory.value = alert;
  historyDialog.value = true;
};

// 获取告警颜色
function getAlertColor(priority) {
  if (priority === "严重") {
    return { background: "#E97659" };
  } else if (priority === "一般严重") {
    return { background: "#FFA059" };
  } else if (priority === "警告") {
    return { background: "#FFC859" };
  } else if (priority === "灾难") {
    return { background: "#E45959" };
  } else {
    return { background: "#7499FF" };
  }
}

// 获取时间轴圆圈颜色 (根据告警状态)
function getTimelineDotColor(status) {
  if (status === '未解决') {
    return { background: "#F56C6C" };  // 未解决：红色
  } else {
    return { background: "#67C23A" };  // 已解决：绿色
  }
}

// 获取状态类型
function getStatusType(status) {
  if (status === '未解决') {
    return "danger";
  } else if (status === '已解决') {
    return "success";
  } else {
    return "info";
  }
}
</script>

<template>
  <div class="alert-timeline">
    <el-card class="timeline-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <i class="el-icon-bell"></i>
            <span>告警历史记录</span>
          </div>
        </div>
      </template>
      
      <div class="timeline-content" v-loading="loading">
        <div class="custom-timeline" :class="{ 'no-data': alerts.length === 0 }">
          <div v-if="alerts.length === 0" class="empty-timeline">
            <el-empty description="暂无告警记录"></el-empty>
          </div>
          <div v-else class="timeline-wrapper">
            <div 
              v-for="(alert, index) in visibleAlerts" 
              :key="index" 
              class="timeline-item"
              :class="[
                alert.status === '未解决' ? 'left-item' : 'right-item'
              ]"
            >
              <div class="timeline-dot" :style="getTimelineDotColor(alert.status)"></div>
              <div class="timeline-content">
                <div class="alert-status-icon" :class="alert.status === '已解决' ? 'resolved' : 'unresolved'">
                  <el-icon v-if="alert.status === '已解决'"><svg-icon name="resolved" /></el-icon>
                  <el-icon v-else><svg-icon name="unresolved" /></el-icon>
                </div>
                <div class="alert-header">
                  <div class="alert-priority">
                    <el-tag 
                      :type="getAlertType(alert.priority).type"
                      :style="{ backgroundColor: getAlertType(alert.priority).color }"
                      effect="dark" 
                      size="small"
                    >
                      {{ alert.priority }}
                    </el-tag>
                    <span class="alert-time">{{ formatDate(alert.lastchange) }}</span>
                  </div>
                  <div class="alert-status">
                    <el-tag 
                      :type="getStatusType(alert.status)" 
                      size="small"
                      effect="light"
                    >
                      {{ alert.status }}
                    </el-tag>
                  </div>
                </div>
                <div class="alert-info">
                  <div class="alert-server">
                    <i class="el-icon-monitor"></i> 
                    {{ alert.hostname }}
                  </div>
                  <div class="alert-desc">
                    {{ alert.description }}
                  </div>
                </div>
                <div class="alert-footer">
                  <div class="alert-ip">IP: {{ alert.hostip }}</div>
                  <el-button 
                    type="text" 
                    size="small" 
                    class="history-button"
                    @click="showAlertHistory(alert)"
                    v-if="alert.histroy_list && alert.histroy_list.length > 0"
                  >
                    <i class="el-icon-time"></i> 历史记录
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 告警历史记录对话框 -->
      <el-dialog
        v-model="historyDialog"
        :title="`告警历史记录 - ${currentAlertHistory?.hostname || ''}`"
        width="70%"
      >
        <div v-if="currentAlertHistory" class="history-dialog-content">
          <div class="alert-detail">
            <div class="alert-detail-item">
              <span class="label">IP地址:</span>
              <span class="value">{{ currentAlertHistory.hostip }}</span>
            </div>
            <div class="alert-detail-item">
              <span class="label">告警内容:</span>
              <span class="value">{{ currentAlertHistory.description }}</span>
            </div>
            <div class="alert-detail-item">
              <span class="label">当前状态:</span>
              <el-tag :type="getStatusType(currentAlertHistory.status)" size="small">
                {{ currentAlertHistory.status }}
              </el-tag>
            </div>
          </div>
          <el-divider />
          <h4 class="history-title">历史记录列表</h4>
          
          <!-- 替换表格为时间线布局 -->
          <div class="history-timeline-content">
            <div 
              v-for="(item, index) in currentAlertHistory.histroy_list" 
              :key="index" 
              class="history-timeline-item"
              :class="item.status === '未解决' || item.status === 'PROBLEM' ? 'left-item' : 'right-item'"
            >
              <div class="history-timeline-dot" :style="{ background: getAlertType(currentAlertHistory.priority).color }"></div>
              <div class="history-timeline-box">
                <div class="alert-status-icon" :class="item.status === '已解决' || item.status === 'OK' ? 'resolved' : 'unresolved'">
                  <el-icon v-if="item.status === '已解决' || item.status === 'OK'"><svg-icon name="resolved" /></el-icon>
                  <el-icon v-else><svg-icon name="unresolved" /></el-icon>
                </div>
                <div class="history-item-header">
                  <div class="history-item-status">
                    <el-tag 
                      :type="getAlertType(currentAlertHistory.priority).type"
                      :style="{ backgroundColor: getAlertType(currentAlertHistory.priority).color }"
                      effect="dark"
                      size="small"
                    >
                      {{ currentAlertHistory.priority }}
                    </el-tag>
                  </div>
                  <div class="history-item-duration">
                    <i class="el-icon-time"></i>
                    <span>{{ item.duration[0] }}{{ item.duration[1] }}{{ item.duration[2] }}{{ item.duration[3] }}</span>
                  </div>
                </div>

                <div class="history-item-times">
                  <div class="history-time-item">
                    <span class="time-label">开始时间:</span>
                    <span class="time-value">{{ item.startime }}</span>
                  </div>
                  <div class="history-time-item" v-if="item.endtime">
                    <span class="time-label">结束时间:</span>
                    <span class="time-value">{{ item.endtime }}</span>
                  </div>
                </div>

                <div class="history-item-tags">
                  <el-tag 
                    v-for="(tag, tagIndex) in item.tags" 
                    :key="tagIndex"
                    effect="plain"
                    size="small"
                    style="margin: 3px 3px"
                  >
                    {{ tag.tag }}:{{ tag.value }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.alert-timeline {
  margin: 20px 0;

  .timeline-card {
  border-radius: 8px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
        
      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 18px;
        }
    
    span {
      font-weight: 600;
      font-size: 16px;
        }
      }
    }
  }
  
  .timeline-content {
    padding: 20px 0;
    
    .custom-timeline {
      position: relative;
      padding: 20px 0;
      
      /* 时间轴固定中心线 */
      &::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 4px;
        height: 100%; /* 使用100% */
        background: linear-gradient(to bottom, #e4e7ed, #409EFF, #e4e7ed); 
        transform: translateX(-50%);
        z-index: 0;
      }
      
      /* 当没有数据时隐藏时间轴 */
      &.no-data::before {
        display: none;
      }
      
      .timeline-wrapper {
        position: relative;
        max-height: 500px;
        overflow-y: auto;
        padding-right: 10px;
        overflow-x: hidden; /* 防止水平滚动 */
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
  
        .timeline-item {
          position: relative;
          margin-bottom: 30px;
          width: 45%;
    
          &.left-item {
            margin-right: 55%;
      
            .timeline-dot {
              right: -44px;
              z-index: 2; /* 确保点在时间线之上 */
            }
      
            .timeline-content {
              &::after {
                right: -10px;
                border-left-color: #fff;
              }
            }
          }
          
          &.right-item {
            margin-left: 55%;
            
            .timeline-dot {
              left: -44px;
              z-index: 2; /* 确保点在时间线之上 */
            }
      
            .timeline-content {
              &::after {
                left: -10px;
                border-right-color: #fff;
                transform: rotate(180deg);
              }
            }
          }
          
          .timeline-dot {
            position: absolute;
            top: 20px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 3px solid #fff; /* 加粗边框 */
            z-index: 2; /* 确保在时间线之上 */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
          }
    
          .timeline-content {
            background: #fff;
            padding: 15px 15px 15px 55px; /* 增加左侧padding留出更大图标位置 */
            border-radius: 8px;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            }
            
            &::after {
        content: '';
        position: absolute;
              top: 20px;
              width: 0;
              height: 0;
              border: 10px solid transparent;
              z-index: 1; /* 确保箭头在适当位置 */
            }
    
            .alert-status-icon {
              position: absolute;
              left: 10px;
              top: 10px;
              width: 38px;
              height: 38px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 2;
              
              .el-icon {
                font-size: 22px;
                
                &.resolved {
                  color: #67C23A;
                }
                
                &.unresolved {
                  color: #F56C6C;
                }
              }
            }
            
            .alert-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;
              
              .alert-priority {
                display: flex;
                align-items: center;
                gap: 8px;
                
                .alert-time {
                  color: #909399;
                  font-size: 12px;
                }
              }
            }
            
            .alert-info {
              margin: 10px 0;
              
              .alert-server {
                font-weight: 500;
                margin-bottom: 5px;
                color: #303133;
                
                i {
                  margin-right: 5px;
                  color: #409EFF;
                }
              }
    
              .alert-desc {
                color: #606266;
                font-size: 13px;
                line-height: 1.5;
              }
            }
            
            .alert-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 10px;
              
              .alert-ip {
                color: #909399;
                font-size: 12px;
              }
              
              .history-button {
                color: #409EFF;
                padding: 2px 6px;
                border: 1px solid #e6f2ff;
                border-radius: 4px;
                
                &:hover {
                  background-color: #e6f2ff;
                }
                
                i {
                  margin-right: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.drawer-content {
  padding: 16px;
  
  .drawer-filters {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .full-timeline {
    position: relative;
    padding: 20px 0;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    
    /* 时间轴线 */
    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 4px; /* 加粗时间轴线 */
      height: 100%; /* 使用100% */
      background: linear-gradient(to bottom, #e4e7ed, #409EFF, #e4e7ed); /* 渐变背景 */
      transform: translateX(-50%);
      z-index: 0; /* 确保线条在底层 */
    }
  }
}

.history-dialog-content {
  max-height: 70vh; /* 限制最大高度 */
  overflow-y: auto; /* 添加滚动 */
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  .alert-detail {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    
    .alert-detail-item {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #606266;
      }
      
      .value {
        color: #303133;
      }
    }
  }
  
  .history-title {
    font-size: 16px;
    font-weight: 600;
    margin: 16px 0;
    color: #303133;
  }
}

.history-timeline-content {
  position: relative;
  padding: 20px 0;
  margin-top: 20px;
  max-height: 400px;
  overflow-y: auto;
  
  /* 时间轴线 */
  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px; /* 加粗时间轴线 */
    height: 100%; /* 使用100% */
    background: linear-gradient(to bottom, #e4e7ed, #409EFF, #e4e7ed); /* 渐变背景 */
    transform: translateX(-50%);
    z-index: 0; /* 确保线条在底层 */
  }
  
  .history-timeline-item {
    position: relative;
    margin-bottom: 30px;
    width: 45%;
    
    &.left-item {
      margin-right: 55%;
      
      .history-timeline-dot {
        right: -44px;
        z-index: 2; /* 确保点在时间线之上 */
      }
      
      .history-timeline-box {
        &::after {
          right: -10px;
          border-left-color: #fff;
        }
      }
    }
    
    &.right-item {
      margin-left: 55%;
      
      .history-timeline-dot {
        left: -44px;
        z-index: 2; /* 确保点在时间线之上 */
      }
      
      .history-timeline-box {
        &::after {
          left: -10px;
          border-right-color: #fff;
          transform: rotate(180deg);
        }
      }
    }
    
    .history-timeline-dot {
      position: absolute;
      top: 20px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 3px solid #fff; /* 加粗边框 */
      z-index: 2; /* 确保在时间线之上 */
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }
    
    .history-timeline-box {
      background: #fff;
      padding: 15px 15px 15px 55px; /* 增加左侧padding留出更大图标位置 */
      border-radius: 8px;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
      position: relative;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 20px;
        width: 0;
        height: 0;
        border: 10px solid transparent;
        z-index: 1; /* 确保箭头在适当位置 */
      }
      
      .alert-status-icon {
        position: absolute;
        left: 10px;
        top: 10px;
        width: 38px;
        height: 38px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        
        .el-icon {
          font-size: 22px;
          
          &.resolved {
            color: #67C23A;
          }
          
          &.unresolved {
            color: #F56C6C;
          }
        }
      }
      
      .history-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .history-item-duration {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #606266;
        font-weight: 500;
        
        i {
          color: #409EFF;
        }
      }
      
      .history-item-times {
        margin: 12px 0;
        
        .history-time-item {
          display: flex;
          margin-bottom: 6px;
          
          .time-label {
            width: 80px;
            color: #606266;
            font-size: 13px;
          }
          
          .time-value {
            color: #303133;
            font-weight: 500;
          }
        }
      }
      
      .history-item-tags {
        margin-top: 10px;
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .timeline-item {
    width: 100% !important;
    margin: 0 0 20px 40px !important;
    
    .timeline-dot {
      left: -30px !important;
      right: auto !important;
    }
    
    .timeline-content {
      &::after {
        left: -10px !important;
        right: auto !important;
        border-right-color: #fff !important;
        transform: rotate(180deg) !important;
      }
    }
  }
  
  .drawer-filters {
    flex-direction: column;
    align-items: flex-start;
    
    .el-input, .el-select {
      width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: 10px;
    }
  }
  
  .history-timeline-item {
    width: 100% !important;
    margin: 0 0 20px 40px !important;
    
    .history-timeline-dot {
      left: -30px !important;
      right: auto !important;
    }
    
    .history-timeline-box {
      &::after {
        left: -10px !important;
        right: auto !important;
        border-right-color: #fff !important;
        transform: rotate(180deg) !important;
      }
    }
  }
  
  .custom-timeline::before,
  .full-timeline::before,
  .history-timeline-content::before {
    left: 20px !important;
    transform: none !important;
  }
}
</style> 

